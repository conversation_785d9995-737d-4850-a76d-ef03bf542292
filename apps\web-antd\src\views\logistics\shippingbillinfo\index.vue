<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { ShippingBillInfoApi } from '#/api/logistics/shippingbillinfo';
import type { ActionItem } from '#/components/table-action/typing';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromBlobPart, isEmpty } from '@vben/utils';

import { message } from 'ant-design-vue';

import { ACTION_ICON, TableAction, useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteShippingBillInfo,
  exportShippingBillInfo,
  getShippingBillInfoPage,
} from '#/api/logistics/shippingbillinfo';
import { $t } from '#/locales';

import { useGridColumns, useGridFormSchema } from './data';
import Form from './modules/form.vue';

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
});

function onRefresh() {
  gridApi.query();
}

function handleCreate() {
  formModalApi.setData({}).open();
}

function handleEdit(row: ShippingBillInfoApi.ShippingBillInfoVO) {
  formModalApi.setData(row).open();
}

async function handleDelete(row: ShippingBillInfoApi.ShippingBillInfoVO) {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting', [row.id]),
    duration: 0,
    key: 'action_process_msg',
  });
  try {
    await deleteShippingBillInfo(row.id as number);
    message.success($t('ui.actionMessage.deleteSuccess', [row.id]));
    onRefresh();
  } finally {
    hideLoading();
  }
}

async function handleDeleteBatch() {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting'),
    duration: 0,
    key: 'action_process_msg',
  });
  try {
    for (const id of checkedIds.value) {
      await deleteShippingBillInfo(id);
    }
    message.success($t('ui.actionMessage.deleteSuccess'));
    onRefresh();
  } finally {
    hideLoading();
  }
}

const checkedIds = ref<number[]>([]);
function handleRowCheckboxChange({
  records,
}: {
  records: ShippingBillInfoApi.ShippingBillInfoVO[];
}) {
  checkedIds.value = records.map((item) => item.id);
}

async function handleExport() {
  const data = await exportShippingBillInfo(await gridApi.formApi.getValues());
  downloadFileFromBlobPart({ fileName: '提单信息.xls', source: data });
}

const toolbarActions: ActionItem[] = [
  {
    label: $t('ui.actionTitle.create', ['提单信息']),
    type: 'primary',
    icon: ACTION_ICON.ADD,
    auth: ['logistics:shipping-bill-info:create'],
    onClick: handleCreate,
  },
  {
    label: $t('ui.actionTitle.export'),
    type: 'primary',
    icon: ACTION_ICON.DOWNLOAD,
    auth: ['logistics:shipping-bill-info:export'],
    onClick: handleExport,
  },
  {
    label: '批量删除',
    type: 'primary',
    danger: true,
    disabled: isEmpty(checkedIds),
    icon: ACTION_ICON.DELETE,
    auth: ['logistics:shipping-bill-info:delete'],
    onClick: handleDeleteBatch,
  },
];

function rowActions(row: ShippingBillInfoApi.ShippingBillInfoVO): ActionItem[] {
  return [
    {
      label: $t('common.edit'),
      type: 'link',
      icon: ACTION_ICON.EDIT,
      auth: ['logistics:shipping-bill-info:update'],
      onClick: handleEdit.bind(null, row),
    },
    {
      label: $t('common.delete'),
      type: 'link',
      danger: true,
      icon: ACTION_ICON.DELETE,
      auth: ['logistics:shipping-bill-info:delete'],
      popConfirm: {
        title: $t('ui.actionMessage.deleteConfirm', [row.billOfLadingNumber]),
        confirm: handleDelete.bind(null, row),
      },
    },
  ];
}

// 分页序号连续
let lastPageInfo = { currentPage: 1, pageSize: 20 };
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: useGridFormSchema(),
  },
  gridOptions: {
    columns: useGridColumns(),
    height: 'auto',
    pagerConfig: { enabled: true },
    seqConfig: {
      seqMethod({ rowIndex }) {
        const { currentPage, pageSize } = lastPageInfo;
        return (currentPage - 1) * pageSize + rowIndex + 1;
      },
    },
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          lastPageInfo = {
            currentPage: page.currentPage,
            pageSize: page.pageSize,
          };
          // 处理创建时间区间
          const params = { ...formValues };
          if (
            Array.isArray(formValues.createTime) &&
            formValues.createTime.length === 2
          ) {
            params.beginCreateTime = formValues.createTime[0];
            params.endCreateTime = formValues.createTime[1];
            delete params.createTime;
          }
          return await getShippingBillInfoPage({
            pageNo: page.currentPage,
            pageSize: page.pageSize,
            ...params,
          });
        },
      },
    },
    rowConfig: { keyField: 'id', isHover: true },
    toolbarConfig: { refresh: { code: 'query' }, search: true },
  } as VxeTableGridOptions<ShippingBillInfoApi.ShippingBillInfoVO>,
  gridEvents: {
    checkboxAll: handleRowCheckboxChange,
    checkboxChange: handleRowCheckboxChange,
  },
});
</script>
<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />
    <Grid table-title="提单信息列表">
      <template #toolbar-tools>
        <TableAction :actions="toolbarActions" />
      </template>
      <template #actions="{ row }">
        <TableAction :actions="rowActions(row)" />
      </template>
    </Grid>
  </Page>
</template>
