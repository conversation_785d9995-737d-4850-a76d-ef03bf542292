import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { WhOrderInfoApi } from '#/api/logistics/whorderinfo';
/** 列表的搜索表单 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      fieldName: 'warehouse',
      label: '入库仓库',
      component: 'ApiSelect',
      rules: 'required',
      componentProps: {
        allowClear: false,
        placeholder: '请选择入库仓库',
        api: async () => {
          const whorderinfoApi = await import('#/api/logistics/whorderinfo');
          const list = await whorderinfoApi.getWarehouseList();
          return list.map((name: string) => ({ label: name, value: name }));
        },
        labelField: 'label',
        valueField: 'value',
      },
    },
    {
      fieldName: 'createTime',
      label: '创建时间',
      component: 'RangePicker',
      componentProps: { allowClear: true },
    },
  ];
}

/** 列表的字段 */
export function useGridColumns(): VxeTableGridOptions<WhOrderInfoApi.WhOrderInfoVO>['columns'] {
  return [
    { type: 'checkbox', width: 40 },
    { type: 'seq', title: '序号', width: 60 },
    { field: 'markNumber', title: '唛头', minWidth: 120 },
    { field: 'trackingNumber', title: '物流条码', minWidth: 120 },
    { field: 'warehouse', title: '入库仓库', minWidth: 120 },
    { field: 'quantity', title: '包装件数', minWidth: 100 },
    { field: 'orderNo', title: '单号', minWidth: 200 },
    { field: 'volume', title: '体积(CBM)', minWidth: 100 },
    { field: 'weight', title: '重量', minWidth: 100 },
    {
      title: '操作',
      width: 160,
      fixed: 'right',
      slots: { default: 'actions' },
    },
  ];
}
