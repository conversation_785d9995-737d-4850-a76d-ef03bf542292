import { requestClient } from '#/api/request';

export namespace AgentInfoApi {
  /** 代理信息 */
  export interface AgentInfoVO {
    id?: number;
    departmentId: number;
    agentName: string;
    agentLogo: string;
    image1Url?: string;
    image2Url?: string;
    queryImage1Url?: string;
    queryImage2Url?: string;
    queryImage3Url?: string;
    queryImage4Url?: string;
    createTime?: string;
    creator?: string;
    updater?: string;
    updateTime?: string;
    deleted?: boolean;
    // 关联字段
    departmentName?: string;
  }

  /** 代理信息分页查询参数 */
  export interface AgentInfoPageReqVO {
    pageNo?: number;
    pageSize?: number;
    agentName?: string;
    departmentId?: number;
    createTime?: [string, string];
  }

  /** 代理信息创建参数 */
  export interface AgentInfoSaveReqVO {
    id?: number;
    departmentId: number;
    agentName: string;
    agentLogo: string;
    image1Url?: string;
    image2Url?: string;
    queryImage1Url?: string;
    queryImage2Url?: string;
    queryImage3Url?: string;
    queryImage4Url?: string;
  }
}

/** 获得代理信息分页 */
export function getAgentInfoPage(params: AgentInfoApi.AgentInfoPageReqVO) {
  return requestClient.get<{ list: AgentInfoApi.AgentInfoVO[]; total: number }>(
    '/logistics/agent-info/page',
    { params },
  );
}

/** 获得代理信息 */
export function getAgentInfo(id: number) {
  return requestClient.get(`/logistics/agent-info/get?id=${id}`);
}

/** 新增代理信息 */
export function createAgentInfo(data: AgentInfoApi.AgentInfoSaveReqVO) {
  return requestClient.post('/logistics/agent-info/create', data);
}

/** 修改代理信息 */
export function updateAgentInfo(data: AgentInfoApi.AgentInfoSaveReqVO) {
  return requestClient.put('/logistics/agent-info/update', data);
}

/** 删除代理信息 */
export function deleteAgentInfo(id: number) {
  return requestClient.delete(`/logistics/agent-info/delete?id=${id}`);
}

/** 导出代理信息 Excel */
export function exportAgentInfo(params: AgentInfoApi.AgentInfoPageReqVO) {
  return requestClient.download('/logistics/agent-info/export-excel', {
    params,
  });
}

/** 获取代理查询图片 */
export async function getAgentQueryImages(agentName?: string) {
  if (!agentName) return null;

  const result = await getAgentInfoPage({
    agentName,
    pageNo: 1,
    pageSize: 1,
  });

  // 返回第一条记录，如果没有记录则返回null
  return result?.list?.[0] || null;
}
