<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Descriptions as ADescriptions } from 'ant-design-vue';

import { getShippingBillInfo } from '#/api/logistics/shippingbillinfo';

const route = useRoute();
const detailData = ref<Record<string, any>>({});
const columns = [
  { label: '提单编号', field: 'billOfLadingNumber' },
  { label: '国际物流编号', field: 'trackingNumber' },
  { label: '预计从中国出发时间', field: 'estimatedDepartureChina' },
  { label: '预计到达贝拉时间', field: 'estimatedArrivalBeira' },
  { label: '中国方借记通知单(USD)', field: 'debitNoteChina' },
  { label: 'Clubfs借记通知单(USD)', field: 'debitNoteClubfs' },
  { label: '中国境内费用', field: 'chinaCost' },
  { label: '关税+运输+仓储', field: 'dutyTransportWarehouse' },
  { label: '利润', field: 'profit' },
  { label: '需退还中国方金额', field: 'refundAmountChina' },
  { label: '创建时间', field: 'createTime' },
  { label: '更新时间', field: 'updateTime' },
  { label: '创建者', field: 'creator' },
  { label: '更新者', field: 'updater' },
];
onMounted(async () => {
  const id = route.params.id;
  if (id) {
    detailData.value = await getShippingBillInfo(Number(id));
  }
});
</script>
<template>
  <ADescriptions :column="2" bordered>
    <a-descriptions-item
      v-for="col in columns"
      :key="col.field"
      :label="col.label"
    >
      {{ detailData[col.field] }}
    </a-descriptions-item>
  </ADescriptions>
</template>
