<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import { Button, ConfigProvider, message } from 'ant-design-vue';
// @ts-ignore
// eslint-disable-next-line n/no-extraneous-import
import html2pdf from 'html2pdf.js';
// @ts-ignore
import QrcodeVue from 'qrcode.vue';

import { useVbenForm } from '#/adapter/form';
import { createOrderInfo } from '#/api/logistics/orderinfo';
import { $t } from '#/locales';

import { useFormSchema } from './data';
import OrderItemGridForm from './OrderItemGridForm.vue';

const [Form, formApi] = useVbenForm({
  commonConfig: {
    componentProps: { class: 'w-full' },
    formItemClass: 'col-span-1',
    labelWidth: 100,
  },
  layout: 'horizontal',
  schema: useFormSchema(),
  showDefaultActions: true,
  handleSubmit,
  wrapperClass: 'grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-2',
});

declare global {
  interface Window {
    __vbenFormApi: any;
    __vbenMarkNumberRef: any;
    __vbenMarkNumberQrRef: any;
    __vbenOrderNoQrRef: any;
  }
}

const orderItemGridRef = ref();
// 打印按钮可用状态
const canPrint = ref(false);
const markNumber = ref('');
const orderNoQrRef = ref('');
const markNumberQrRef = ref('');
// 挂载formApi和markNumber到window，供data.ts onChange使用
if (typeof window !== 'undefined') {
  window.__vbenFormApi = formApi;
  window.__vbenMarkNumberRef = markNumber;
  window.__vbenMarkNumberQrRef = markNumberQrRef;
  window.__vbenOrderNoQrRef = orderNoQrRef;
}

// 保存草稿 key
const DRAFT_KEY = 'orderInfoDraft';

// 保存草稿方法
async function handleSaveDraft() {
  const values = await formApi.getValues();
  const items = orderItemGridRef.value?.getData() ?? [];
  const draft = { values, items };
  localStorage.setItem(DRAFT_KEY, JSON.stringify(draft));
  message.success('草稿已保存到本地');
}

// 页面加载时自动恢复草稿
onMounted(async () => {
  const draftStr = localStorage.getItem(DRAFT_KEY);
  if (draftStr) {
    try {
      const draft = JSON.parse(draftStr);
      if (draft.values) {
        await formApi.setValues(draft.values);
        // 恢复二维码内容
        orderNoQrRef.value = draft.values.orderNo || '';
        markNumberQrRef.value = draft.values.markNumber || '';
      }
      if (draft.items) orderItemGridRef.value?.setData(draft.items);
      message.info('已自动恢复本地草稿');
    } catch {}
  }
});

const canSaveDraft = ref(true);

async function handleSubmit(values: any) {
  // 通过子控件获取明细数据
  const items = (orderItemGridRef.value?.getData() as any[]) ?? [];
  if (items.length === 0) {
    message.error('请录入装箱明细');
    return;
  }
  // 校验每一行商品名称
  for (const [i, item] of items.entries()) {
    if (!item.itemName || !item.itemName.trim()) {
      message.error(`第${i + 1}行商品名称不能为空`);
      return;
    }
  }
  if ('id' in values) delete values.id;
  // 合并主表和子表数据
  const payload = { ...values, orderItems: items };
  const res = await createOrderInfo(payload as any);
  // 返回ordreNO 填充表单后才能正式打印
  formApi.setValues({ orderNo: res });
  if (typeof window !== 'undefined' && window.__vbenOrderNoQrRef) {
    window.__vbenOrderNoQrRef.value = res;
  }
  // 主动赋值二维码内容
  orderNoQrRef.value = res || '';
  // 如果 markNumber 也需要二维码，主动赋值
  const markNumber = (values.markNumber || '').toString();
  markNumberQrRef.value = markNumber;
  // 新增：打印按钮可用状态
  canPrint.value = true;
  message.success($t('ui.actionMessage.operationSuccess'));
  // 提交成功后清除草稿
  localStorage.removeItem(DRAFT_KEY);
  // 提交成功后自动下载 PDF
  handleDownloadPDF();
  // 提交后禁用保存草稿按钮
  canSaveDraft.value = false;
}

function handleReset() {
  formApi.resetForm();
  orderItemGridRef.value?.setData([]);
  canPrint.value = false;
  localStorage.removeItem(DRAFT_KEY); // 清空草稿
  // 清空二维码内容
  orderNoQrRef.value = '';
  markNumberQrRef.value = '';
  // 重置后允许保存草稿
  canSaveDraft.value = true;
}
function handlePrint() {
  window.print();
}
function handleDownloadPDF() {
  const element = document.querySelector('.print-main');
  const btns = document.querySelectorAll('.no-print');
  btns.forEach((btn) => ((btn as HTMLElement).style.display = 'none'));
  if (!element) {
    message.error('未找到可导出的内容区域');
    btns.forEach((btn) => ((btn as HTMLElement).style.display = ''));
    return;
  }
  formApi.getValues().then((values) => {
    const orderNo = values.orderNo || '';
    const filename = orderNo
      ? `Delivery Note -${orderNo}.pdf`
      : 'Delivery Note.pdf';
    html2pdf()
      .set({
        margin: [10, 10, 10, 10],
        filename,
        html2canvas: { scale: 2 },
        jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' },
      })
      .from(element)
      .save()
      .finally(() => {
        btns.forEach((btn) => ((btn as HTMLElement).style.display = ''));
      });
  });
}
</script>
<template>
  <ConfigProvider>
    <!-- 页面整体背景美化 -->
    <div
      class="print-main mt-4 flex min-h-screen flex-col items-center bg-gradient-to-br from-gray-50 via-white to-gray-100"
    >
      <!-- logo -->
      <div class="flex w-full justify-center">
        <div class="w-full max-w-screen-2xl px-4">
          <img src="./d1_logo.jpg" alt="Logo" style="height: 60px" />
        </div>
      </div>
      <!-- 内容区 -->
      <div class="mt-6 flex w-full max-w-screen-2xl items-stretch">
        <!-- 左侧图片 -->
        <div class="flex flex-1 items-start justify-end pb-4 pl-4 pr-4">
          <img
            src="./65e194bfb6c00.jpg"
            alt="Left"
            class="mb-0 mt-0 block max-h-[700px] w-full self-start rounded object-contain align-top shadow-md"
            style="max-width: 100%; vertical-align: top"
          />
        </div>
        <!-- 中间表单内容 -->
        <div
          class="relative z-10 flex w-full max-w-4xl flex-col justify-start rounded-2xl border border-gray-200 bg-white/90 p-8 text-lg shadow-2xl md:text-xl"
        >
          <!-- 新增：红色加粗居中标题和副标题 -->
          <div class="mb-4 w-full text-center">
            <div
              style="
                font-size: 3rem;
                font-weight: bold;
                color: black;
                letter-spacing: 2px;
              "
            >
              Delivery Note
            </div>
            <div
              style="
                font-size: 1.1rem;
                font-weight: bold;
                color: #e53935;
                margin-top: 1.45rem;
                letter-spacing: 1px;
              "
            >
              送货前请先联系 Sean 18188802770( 微信）填写此单贴在外包装上
            </div>
          </div>
          <!-- 子表移到Form内部，二维码区、Grid、按钮区、注意事项区均 col-span-full -->
          <Form @submit="handleSubmit">
            <template #orderNoQr>
              <div class="col-span-full mb-2 flex items-center justify-center">
                <QrcodeVue
                  v-if="orderNoQrRef"
                  :value="orderNoQrRef"
                  :size="100"
                />
              </div>
            </template>
            <template #markNumberQr>
              <div class="col-span-full mb-2 flex items-center justify-center">
                <QrcodeVue
                  v-if="markNumberQrRef"
                  :value="markNumberQrRef"
                  :size="100"
                />
              </div>
            </template>
            <!-- 其余表单项自动渲染 -->
            <div class="col-span-full mb-4">
              <OrderItemGridForm
                ref="orderItemGridRef"
                style="min-width: 100px; width: 100%; max-width: 1000px"
              />
            </div>
            <div class="no-print col-span-full mb-4">
              <div class="flex justify-end gap-4">
                <Button
                  native-type="button"
                  type="primary"
                  ghost
                  class="rounded shadow"
                  @click="orderItemGridRef.onAdd()"
                >
                  新增装箱明细
                </Button>
                <Button
                  native-type="button"
                  type="default"
                  class="rounded shadow"
                  @click="handleSaveDraft"
                  :disabled="!canSaveDraft"
                >
                  保存草稿
                </Button>
                <Button
                  @click="handleReset"
                  type="default"
                  class="rounded shadow"
                >
                  重置
                </Button>
                <Button
                  html-type="submit"
                  type="primary"
                  class="rounded shadow"
                >
                  提交
                </Button>
                <Button
                  @click="handlePrint"
                  :disabled="!canPrint"
                  type="default"
                  class="rounded shadow"
                >
                  打印
                </Button>
                <Button
                  @click="handleDownloadPDF"
                  :disabled="!canPrint"
                  type="default"
                  class="rounded shadow"
                >
                  下载PDF
                </Button>
              </div>
            </div>
            <div class="col-span-full mb-2">
              <div
                class="w-full max-w-2xl rounded border-l-4 border-red-400 bg-red-50 p-4 text-left shadow-sm"
              >
                <div
                  style="
                    font-weight: bold;
                    color: #b71c1c;
                    font-size: 1.1rem;
                    margin-bottom: 0.5rem;
                  "
                >
                  表单注意事项：
                </div>
                <ul class="space-y-1 pl-4 text-sm leading-tight text-gray-800">
                  <li>
                    Warehouse address 仓库地址 :
                    佛山顺德区乐从镇新隆村第一工业区内 101
                  </li>
                  <li>Sean: +8618188802770</li>
                  <li>上班时间：周一到周六，上午 10 点到下午 6 点。</li>
                  <li>本仓库不收到付件，不收无唛头的件。</li>
                  <li>仓库没有免费卸货服务，如需卸货，请联系仓库咨询费用。</li>
                </ul>
              </div>
            </div>
          </Form>
        </div>
        <!-- 右侧图片 -->
        <div class="flex flex-1 items-start justify-start pb-4 pl-4 pr-4">
          <img
            src="./65e19528ba249.jpg"
            alt="Right"
            class="mb-0 mt-0 block max-h-[700px] w-full self-start rounded object-contain align-top shadow-md"
            style="max-width: 100%; vertical-align: top"
          />
        </div>
      </div>
    </div>
  </ConfigProvider>
</template>

<style>
@media print {
  html,
  body {
    height: auto;
    zoom: 1 !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
  @page {
    size: A4;
    margin: 16mm 10mm 16mm 10mm;
  }
  /* 保持表单双列布局 */
  .grid {
    display: grid !important;
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
    gap: 2rem 2rem !important;
  }
  /* 避免表单、表格等被拆页 */
  .no-break,
  .ant-form,
  .ant-table,
  .print-main {
    page-break-inside: avoid;
    break-inside: avoid;
  }
  /* 避免内容区被拆页，防止空白页 */
  .print-main {
    page-break-before: avoid;
    page-break-after: avoid;
    break-before: avoid;
    break-after: avoid;
    min-height: auto !important;
    height: auto !important;
  }
  .min-h-screen {
    min-height: auto !important;
    height: auto !important;
  }
  /* 隐藏不需要打印的内容 */
  .no-print,
  .ant-btn,
  .ant-message {
    display: none !important;
  }
}
</style>
