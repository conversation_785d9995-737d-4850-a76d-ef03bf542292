# Delivery Note 打印和PDF功能

## 功能概述

本功能实现了基于Excel模板的打印和PDF生成，采用**Excel模板转HTML，统一格式输出**的架构：

1. **静态Excel模板**：预先生成的Excel模板文件，包含所有格式和样式
2. **模板转HTML**：将Excel模板转换为HTML格式，用于打印和PDF生成
3. **统一数据源**：打印、PDF都使用相同的Excel模板，确保格式完全一致
4. **高性能**：模板一次生成，后续仅填充数据并转换格式

## 架构设计

### 1. 静态模板文件
- **位置**: `/public/templates/delivery-note-template.xlsx`
- **生成**: 通过 `scripts/generate-excel-template.js` 脚本生成
- **内容**: 包含所有格式、样式和占位符（如 `{{MARK_NUMBER}}`）

### 2. 模板转换工具 (`/src/utils/excel-template.ts`)
- `convertExcelTemplateToHTML()`: 将Excel模板转换为HTML格式
- `fillDeliveryNoteTemplate()`: 读取模板并填充数据（已废弃）
- `DeliveryNoteData`: 数据接口定义

### 3. 表单集成 (`OrderInfoFormPage.vue`)
- `handlePrint()`: 使用Excel模板的打印功能
- `handleDownloadPDF()`: 使用Excel模板的PDF生成功能
- 移除了Excel下载功能

## 使用方法

1. **填写表单**：在订单表单中填写必要信息
2. **提交表单**：点击"提交"按钮生成单号
3. **打印**：点击"打印"按钮进行打印
4. **下载PDF**：点击"下载PDF"按钮生成PDF文件

## Excel 模板格式

生成的 Excel 文件包含以下内容：

- **T&C Logo 区域**：公司标识和名称
- **主标题**：Delivery Note
- **序列号**：679（可配置）
- **红色提示文字**：送货前联系信息
- **主要信息表格**：
  - 唛头 (Mark)：显示唛头信息和运输方式标识
  - 品名 (Item Name)：显示单号（根据需求，品名就是单号）
  - 件数 (PACKAGES)：显示包装件数
  - 条形码区域：唛头条形码
- **仓库信息**：地址、联系方式、注意事项
- **大号唛头显示**：页面底部大字体显示唛头

## 数据映射

| Excel 字段 | 表单字段 | 说明 |
|-----------|---------|------|
| 唛头 Mark | markNumber | 自动生成的唛头 |
| 品名 Item Name | orderNo | 单号作为品名 |
| 件数 PACKAGES | quantity | 包装件数 |
| 运输方式标识 | shippingMethod | 1=空运, 2=海运 |
| 序列号 | 固定值 | 679 |

## 技术实现

- **Excel 处理库**：使用 `exceljs` 库读取模板
- **模板系统**：静态Excel模板 + 占位符替换 + HTML转换
- **打印功能**：将HTML内容写入新窗口进行打印
- **PDF生成**：使用 `html2pdf.js` 将HTML转换为PDF
- **样式设置**：预设在静态模板中，转换为HTML时保持样式
- **条形码**：文本形式的条形码表示

## 模板占位符

静态模板中使用以下占位符：

| 占位符 | 说明 | 数据来源 |
|--------|------|----------|
| `{{SERIAL_NUMBER}}` | 序列号 | 固定值 "679" |
| `{{MARK_NUMBER}}` | 唛头 | formData.markNumber |
| `{{SHIPPING_METHOD}}` | 运输方式标识 | 根据 shippingMethod 生成 |
| `{{BARCODE}}` | 条形码 | 根据 markNumber 生成 |
| `{{ORDER_NO}}` | 单号（品名） | formData.orderNo |
| `{{QR_CODE}}` | 二维码 | 固定样式 |
| `{{QUANTITY}}` | 件数 | formData.quantity |
| `{{BIG_MARK_NUMBER}}` | 大号唛头 | formData.markNumber |

## 模板重新生成

如需修改模板格式，运行以下命令：

```bash
cd apps/web-antd
node scripts/generate-excel-template.js
```

这将重新生成 `/public/templates/delivery-note-template.xlsx` 文件。

## 注意事项

1. **模板文件**：静态模板文件位于 `public/templates/` 目录，部署时需要确保该文件存在
2. **数据填充**：需要先提交表单生成单号后才能打印或生成PDF
3. **文件命名**：PDF 文件名格式：`delivery-note-{单号}.pdf`
4. **运输方式**：支持空运(AIR)、海运(SEA)的自动标识
5. **性能优化**：使用模板转换方式，避免每次重新创建格式
6. **条形码**：以文本形式显示，可根据需要集成真实条形码库
7. **浏览器兼容性**：打印功能需要浏览器支持弹窗，PDF生成需要支持Canvas
