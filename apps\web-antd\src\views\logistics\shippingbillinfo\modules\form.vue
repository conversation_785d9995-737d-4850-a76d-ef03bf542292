<script lang="ts" setup>
import type { ShippingBillInfoApi } from '#/api/logistics/shippingbillinfo';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  createShippingBillInfo,
  getShippingBillInfo,
  updateShippingBillInfo,
} from '#/api/logistics/shippingbillinfo';
import { $t } from '#/locales';

const emit = defineEmits(['success']);
const formData = ref<ShippingBillInfoApi.ShippingBillInfoVO>();

const getTitle = computed(() => {
  return formData.value?.id
    ? $t('ui.actionTitle.edit', ['提单信息'])
    : $t('ui.actionTitle.create', ['提单信息']);
});

const [Form, formApi] = useVbenForm({
  commonConfig: {
    componentProps: { class: 'w-full' },
    formItemClass: 'col-span-2',
    labelWidth: 120,
  },
  layout: 'horizontal',
  schema: [
    {
      fieldName: 'billOfLadingNumber',
      label: '提单编号',
      component: 'Input',
      rules: 'required',
      componentProps: { placeholder: '请输入提单编号' },
    },
    {
      fieldName: 'trackingNumber',
      label: '国际物流编号',
      component: 'Input',
      rules: 'required',
      componentProps: { placeholder: '请输入国际物流编号' },
    },
    {
      fieldName: 'estimatedDepartureChina',
      label: '预计从中国出发时间',
      component: 'DatePicker',
      componentProps: { placeholder: '请选择日期' },
    },
    {
      fieldName: 'estimatedArrivalBeira',
      label: '预计到达贝拉时间',
      component: 'DatePicker',
      componentProps: { placeholder: '请选择日期' },
    },
    {
      fieldName: 'debitNoteChina',
      label: '中国方借记通知单(USD)',
      component: 'InputNumber',
      rules: 'required',
      componentProps: { min: 0, placeholder: '请输入金额' },
    },
    {
      fieldName: 'debitNoteClubfs',
      label: 'Clubfs借记通知单(USD)',
      component: 'InputNumber',
      rules: 'required',
      componentProps: { min: 0, placeholder: '请输入金额' },
    },
    {
      fieldName: 'chinaCost',
      label: '中国境内费用',
      component: 'InputNumber',
      rules: 'required',
      componentProps: { min: 0, placeholder: '请输入金额' },
    },
    {
      fieldName: 'dutyTransportWarehouse',
      label: '关税+运输+仓储',
      component: 'InputNumber',
      rules: 'required',
      componentProps: { min: 0, placeholder: '请输入金额' },
    },
    {
      fieldName: 'profit',
      label: '利润',
      component: 'InputNumber',
      rules: 'required',
      componentProps: { min: 0, placeholder: '请输入金额' },
    },
    {
      fieldName: 'refundAmountChina',
      label: '需退还中国方金额',
      component: 'InputNumber',
      rules: 'required',
      componentProps: { min: 0, placeholder: '请输入金额' },
    },
  ],
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    modalApi.lock();
    const data =
      (await formApi.getValues()) as ShippingBillInfoApi.ShippingBillInfoVO;
    try {
      await (formData.value?.id
        ? updateShippingBillInfo({ ...formData.value, ...data })
        : createShippingBillInfo(data));
      await modalApi.close();
      emit('success');
      message.success({
        content: $t('ui.actionMessage.operationSuccess'),
        key: 'action_process_msg',
      });
    } finally {
      modalApi.unlock();
    }
  },
  async onOpenChange(isOpen: boolean) {
    if (!isOpen) {
      formData.value = undefined;
      return;
    }
    let data = modalApi.getData<ShippingBillInfoApi.ShippingBillInfoVO>();
    if (!data) return;
    if (data.id) {
      modalApi.lock();
      try {
        data = await getShippingBillInfo(data.id);
      } finally {
        modalApi.unlock();
      }
    }
    formData.value = data;
    await formApi.setValues(formData.value);
  },
});
</script>
<template>
  <Modal :title="getTitle">
    <Form class="mx-4" />
  </Modal>
</template>
