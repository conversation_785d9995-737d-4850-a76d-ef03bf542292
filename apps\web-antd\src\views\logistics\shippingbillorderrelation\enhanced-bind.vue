<script lang="ts" setup>
import type { ShippingBillInfoApi } from '#/api/logistics/shippingbillinfo';
import type { ShippingBillOrderRelationApi } from '#/api/logistics/shippingbillorderrelation';

import { computed, ref } from 'vue';

import { Page } from '@vben/common-ui';

import BillList from './modules/bill-list.vue';
import BoundOrdersList from './modules/bound-orders-list.vue';
import CurrentBillInfo from './modules/current-bill-info.vue';
import OrderList from './modules/order-list.vue';

// 提单相关
const selectedBillNo = ref<string>('');
const selectedBillInfo = ref<ShippingBillInfoApi.ShippingBillInfoVO>();

// 绑定关系相关
const boundOrders = ref<
  ShippingBillOrderRelationApi.ShippingBillOrderRelationVO[]
>([]);

// 组件引用
const boundOrdersListRef = ref();

// 计算已绑定的订单号列表
const boundOrderNos = computed(() =>
  boundOrders.value.map((order) => order.orderNo),
);

// 提单选择处理
function handleBillSelect(
  billNo: string,
  billInfo?: ShippingBillInfoApi.ShippingBillInfoVO,
) {
  selectedBillNo.value = billNo;
  selectedBillInfo.value = billInfo;
}

// 绑定成功处理
function handleBindSuccess() {
  // 刷新已绑定订单列表
  if (boundOrdersListRef.value) {
    boundOrdersListRef.value.fetchBoundOrders();
  }
}

// 刷新处理
function handleRefresh() {
  // 可以在这里添加其他需要刷新的逻辑
}

// 更新绑定订单数据
function handleBoundOrdersUpdate(orders: ShippingBillOrderRelationApi.ShippingBillOrderRelationVO[]) {
  boundOrders.value = orders;
}
</script>

<template>
  <Page auto-content-height>
    <a-card title="提单-订单绑定管理">
      <a-row :gutter="16">
        <!-- 左侧：提单列表 -->
        <a-col :span="8">
          <BillList
            :selected-bill-no="selectedBillNo"
            @select="handleBillSelect"
          />
        </a-col>

        <!-- 右侧：绑定管理区域 -->
        <a-col :span="16">
          <div
            v-if="!selectedBillNo"
            style="text-align: center; padding: 64px 0"
          >
            <a-empty description="请先选择一个提单" />
          </div>

          <div v-else style="display: flex; flex-direction: column; gap: 16px">
            <!-- 当前提单信息 -->
            <CurrentBillInfo :bill-info="selectedBillInfo" />



            <a-row :gutter="16">
              <!-- 已绑定订单 -->
              <a-col :span="12">
                <BoundOrdersList
                  ref="boundOrdersListRef"
                  :bill-no="selectedBillNo"
                  @refresh="handleRefresh"
                  @update="handleBoundOrdersUpdate"
                />
              </a-col>

              <!-- 订单列表 -->
              <a-col :span="12">
                <OrderList
                  :selected-bill-no="selectedBillNo"
                  :bound-order-nos="boundOrderNos"
                  @bind-success="handleBindSuccess"
                />
              </a-col>
            </a-row>
          </div>
        </a-col>
      </a-row>
    </a-card>
  </Page>
</template>
