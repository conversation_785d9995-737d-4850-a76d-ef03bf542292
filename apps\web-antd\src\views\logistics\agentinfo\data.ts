import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { AgentInfoApi } from '#/api/logistics/agentinfo';

import { getDeptList } from '#/api/system/dept';
import { getRangePickerDefaultProps } from '#/utils';

/** 新增/修改的表单 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      fieldName: 'id',
      component: 'Input',
      dependencies: {
        triggerFields: [''],
        show: () => false,
      },
    },
    {
      fieldName: 'agentName',
      label: '代理名称',
      rules: 'required',
      component: 'Input',
      componentProps: {
        placeholder: '请输入代理名称',
      },
    },
    {
      fieldName: 'departmentId',
      label: '所属部门',
      rules: 'required',
      component: 'ApiTreeSelect',
      componentProps: {
        placeholder: '请选择所属部门',
        api: getDeptList,
        fieldNames: {
          label: 'name',
          value: 'id',
          children: 'children',
        },
        treeDefaultExpandAll: true,
      },
    },
    {
      fieldName: 'agentLogo',
      label: '代理Logo',
      rules: 'required',
      component: 'ImageUpload',
      componentProps: {
        limit: 1,
        fileSize: 5,
        fileType: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
        width: '120px',
        height: '80px',
      },
    },
    {
      fieldName: 'image1Url',
      label: '展示图片1',
      component: 'ImageUpload',
      componentProps: {
        limit: 1,
        fileSize: 5,
        fileType: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
        width: '100px',
        height: '80px',
      },
    },
    {
      fieldName: 'image2Url',
      label: '展示图片2',
      component: 'ImageUpload',
      componentProps: {
        limit: 1,
        fileSize: 5,
        fileType: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
        width: '100px',
        height: '80px',
      },
    },
    {
      fieldName: 'queryImage1Url',
      label: '查询图片1',
      component: 'ImageUpload',
      componentProps: {
        limit: 1,
        fileSize: 5,
        fileType: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
        width: '100px',
        height: '80px',
      },
    },
    {
      fieldName: 'queryImage2Url',
      label: '查询图片2',
      component: 'ImageUpload',
      componentProps: {
        limit: 1,
        fileSize: 5,
        fileType: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
        width: '100px',
        height: '80px',
      },
    },
    {
      fieldName: 'queryImage3Url',
      label: '查询图片3',
      component: 'ImageUpload',
      componentProps: {
        limit: 1,
        fileSize: 5,
        fileType: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
        width: '100px',
        height: '80px',
      },
    },
    {
      fieldName: 'queryImage4Url',
      label: '查询图片4',
      component: 'ImageUpload',
      componentProps: {
        limit: 1,
        fileSize: 5,
        fileType: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
        width: '100px',
        height: '80px',
      },
    },
  ];
}

/** 列表的搜索表单 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      fieldName: 'agentName',
      label: '代理名称',
      component: 'Input',
      componentProps: {
        allowClear: true,
        placeholder: '请输入代理名称',
      },
    },
    {
      fieldName: 'departmentId',
      label: '所属部门',
      component: 'ApiTreeSelect',
      componentProps: {
        allowClear: true,
        placeholder: '请选择所属部门',
        api: getDeptList,
        fieldNames: {
          label: 'name',
          value: 'id',
          children: 'children',
        },
        treeDefaultExpandAll: true,
      },
    },
    {
      fieldName: 'createTime',
      label: '创建时间',
      component: 'RangePicker',
      componentProps: {
        ...getRangePickerDefaultProps(),
        allowClear: true,
      },
    },
  ];
}

/** 列表的字段 */
export function useGridColumns(): VxeTableGridOptions<AgentInfoApi.AgentInfoVO>['columns'] {
  return [
    { type: 'checkbox', width: 40 },
    {
      field: 'id',
      title: '编号',
      minWidth: 80,
    },
    {
      field: 'agentName',
      title: '代理名称',
      minWidth: 150,
    },
    {
      field: 'departmentName',
      title: '所属部门',
      minWidth: 120,
      formatter: ({ cellValue }) => cellValue || '-',
    },
    {
      field: 'agentLogo',
      title: '代理Logo',
      minWidth: 100,
      slots: { default: 'agentLogo' },
    },
    {
      field: 'images',
      title: '展示图片',
      minWidth: 120,
      slots: { default: 'images' },
    },
    {
      field: 'queryImages',
      title: '查询图片',
      minWidth: 150,
      slots: { default: 'queryImages' },
    },
    {
      field: 'createTime',
      title: '创建时间',
      minWidth: 180,
      formatter: 'formatDateTime',
    },
    {
      title: '操作',
      width: 220,
      fixed: 'right',
      slots: { default: 'actions' },
    },
  ];
}
