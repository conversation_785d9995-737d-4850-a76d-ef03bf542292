import { convertExcelTemplateToHTML } from '../src/utils/excel-template.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * 测试Excel模板转HTML功能
 */
async function testHTMLGeneration() {
  try {
    // 测试数据
    const testData = {
      formData: {
        orderNo: 'ORD-2025-001',
        markNumber: '12345-SEA',
        customerName: '测试客户',
        customerTel: '13800138000',
        supplierName: '测试供应商',
        supplierTel: '13900139000',
        shippingMethod: 2, // 海运
        warehouse: '仓库101',
        quantity: 5,
        volume: 2.5,
        weight: 100,
        trackingNumber: 'TRK123456',
        createTime: '2025-01-29',
      },
      orderItems: [
        { itemName: '商品1', quantity: 2 },
        { itemName: '商品2', quantity: 3 },
      ],
      logoUrl: '',
      currentDate: '2025-01-29',
      warehouseCode: 'clubfs.warehouse101.cn/#',
      serialNumber: '679',
    };

    console.log('🧪 开始测试Excel模板转HTML功能...');

    // 生成HTML
    const htmlContent = await convertExcelTemplateToHTML(testData);
    
    console.log('✅ HTML生成成功');

    // 保存测试文件
    const outputPath = path.join(__dirname, '../public/test-html-output.html');
    fs.writeFileSync(outputPath, htmlContent, 'utf8');
    
    console.log('✅ 测试HTML文件已生成:', outputPath);
    console.log('🎉 Excel模板转HTML功能测试成功！');
    console.log('📝 可以在浏览器中打开测试文件查看效果');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
testHTMLGeneration();
