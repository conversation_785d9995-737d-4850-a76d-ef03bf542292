<script lang="ts" setup>
import type { OnActionClickParams } from '#/adapter/vxe-table';

import { nextTick, watch } from 'vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

// 子表数据结构
interface OrderItem {
  id?: number;
  boxNumber?: string;
  itemNumber?: string;
  itemName?: string;
  quantity?: number;
}

const props = defineProps<{
  items?: OrderItem[]; // 初始明细
  orderNo?: string; // 主表单号
}>();

function onActionClick({ code, row }: OnActionClickParams<OrderItem>) {
  switch (code) {
    case 'delete': {
      onDelete(row);
      break;
    }
  }
}

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: [
      { type: 'seq', title: '序号', width: 60 },
      {
        field: 'itemName',
        title: '商品名称',
        minWidth: 120,
        editRender: { name: 'input', attrs: { placeholder: '请输入商品名称' } },
      },
      {
        field: 'quantity',
        title: '商品数量',
        minWidth: 100,
        editRender: {
          name: 'input',
          attrs: { type: 'number', min: 1, placeholder: '请输入商品数量' },
        },
      },
      {
        field: 'operation',
        title: '操作',
        minWidth: 60,
        align: 'center',
        cellRender: {
          name: 'CellOperation',
          options: [{ code: 'delete', text: '删除', danger: true }],
          attrs: { onClick: onActionClick, class: 'no-print' },
        },
      },
    ],
    border: true,
    showOverflow: true,
    autoResize: true,
    keepSource: true,
    rowConfig: { keyField: 'id' },
    pagerConfig: { enabled: false },
    toolbarConfig: { enabled: false },
    editConfig: { trigger: 'click', mode: 'cell' },
    validConfig: { message: 'default' },
  },
});

const onAdd = async () => {
  await gridApi.grid.insertAt({} as OrderItem, -1);
};
const onDelete = async (row: OrderItem) => {
  await gridApi.grid.remove(row);
};

defineExpose({
  getData: (): OrderItem[] => {
    const data = gridApi.grid.getData() as OrderItem[];
    const removeRecords = gridApi.grid.getRemoveRecords() as OrderItem[];
    const insertRecords = gridApi.grid.getInsertRecords() as OrderItem[];
    // 只保留需要上传的字段
    return [
      ...data.filter(
        (row) => !removeRecords.some((removed) => removed.id === row.id),
      ),
      ...insertRecords.map((row: any) => ({ ...row, id: undefined })),
    ].map(({ boxNumber, itemNumber, itemName, quantity }) => ({
      boxNumber,
      itemNumber,
      itemName,
      quantity,
    }));
  },
  setData: (items: OrderItem[]) => {
    gridApi.grid.loadData(items || []);
  },
  onAdd,
  validate: () => undefined,
});

// 初始化/监听主表 orderNo 或 items 变化，自动刷新子表数据
watch(
  () => props.items,
  async (val) => {
    await nextTick();
    gridApi.grid.loadData(val || []);
  },
  { immediate: true },
);
</script>

<template>
  <div class="relative" style="max-width: 900px; min-width: 500px">
    <Grid
      class="mx-1"
      style="
        max-width: 900px;
        min-width: 500px;
        font-size: 13px;
        line-height: 1.2;
        --vben-vxe-table-row-padding-y: 2px;
        --vben-vxe-table-row-padding-x: 4px;
      "
    />
  </div>
</template>
