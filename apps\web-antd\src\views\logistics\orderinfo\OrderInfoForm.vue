<template>
  <Modal :title="getTitle">
    <Form class="mx-4" />
  </Modal>
</template>
<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useVbenForm } from '#/adapter/form'
import { useVbenModal } from '@vben/common-ui'
import { message } from 'ant-design-vue'
import type { OrderInfoApi } from '../../../api/logistics/orderinfo'
import { getOrderInfo, createOrderInfo, updateOrderInfo } from '../../../api/logistics/orderinfo'
import { $t } from '../../../locales'
import { useFormSchema } from './data'

const emit = defineEmits(['success'])
const formData = ref<OrderInfoApi.OrderInfoVO>()
const getTitle = computed(() => {
  return formData.value?.id
    ? $t('ui.actionTitle.edit', ['物流订单入仓'])
    : $t('ui.actionTitle.create', ['物流订单入仓'])
})

const [Form, formApi] = useVbenForm({
  commonConfig: {
    componentProps: { class: 'w-full' },
    formItemClass: 'col-span-2',
    labelWidth: 100,
  },
  layout: 'horizontal',
  schema: useFormSchema(),
  showDefaultActions: false,
})

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate()
    if (!valid) return
    modalApi.lock()
    const data = (await formApi.getValues()) as OrderInfoApi.OrderInfoVO
    try {
      if (formData.value?.id) {
        await updateOrderInfo(data)
      } else {
        await createOrderInfo(data)
      }
      await modalApi.close()
      emit('success')
      message.success({ content: $t('ui.actionMessage.operationSuccess'), key: 'action_process_msg' })
    } finally {
      modalApi.unlock()
    }
  },
  async onOpenChange(isOpen: boolean) {
    if (!isOpen) {
      formData.value = undefined
      return
    }
    let data = modalApi.getData<OrderInfoApi.OrderInfoVO>()
    if (!data) return
    if (data.id) {
      modalApi.lock()
      try {
        data = await getOrderInfo(data.id)
      } finally {
        modalApi.unlock()
      }
    }
    formData.value = data
    await formApi.setValues(formData.value)
  },
})
</script>
