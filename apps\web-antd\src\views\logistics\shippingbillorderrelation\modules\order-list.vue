<script lang="ts" setup>
import type { OrderInfoApi } from '#/api/logistics/orderinfo';

import { computed, onMounted, ref } from 'vue';

import { message } from 'ant-design-vue';

import { getOrderInfoPage } from '#/api/logistics/orderinfo';
import { batchBindOrdersToBill } from '#/api/logistics/shippingbillorderrelation';

interface Props {
  selectedBillNo: string;
  boundOrderNos: string[];
}

interface Emits {
  (e: 'bindSuccess'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 搜索文本
const searchText = ref('');

// 订单相关
const orderList = ref<OrderInfoApi.OrderInfoVO[]>([]);
const loading = ref(false);
const pagination = ref({ current: 1, pageSize: 10, total: 0 });
const bindingOrderNo = ref<string>('');

// 每个订单的未结清费用
const orderOutstandingCharges = ref<Record<string, number | undefined>>({});

// 批量选择相关
const selectedRowKeys = ref<string[]>([]);
const batchBinding = ref(false);

// 表格列定义
const columns = [
  { title: '订单号', dataIndex: 'orderNo', key: 'orderNo', width: 120 },
  {
    title: '物流条码',
    dataIndex: 'trackingNumber',
    key: 'trackingNumber',
    width: 120,
  },
  { title: '未结清费用', key: 'outstandingCharges', width: 140 },
  {
    title: '客户名称',
    dataIndex: 'customerName',
    key: 'customerName',
    width: 120,
  },
  { title: '仓库', dataIndex: 'warehouse', key: 'warehouse', width: 100 },
  { title: '操作', key: 'action', width: 80, fixed: 'right' },
];

// 过滤后的列表
const filteredOrderList = computed(() => {
  if (!searchText.value) return orderList.value;
  return orderList.value.filter((order) =>
    order.orderNo.toLowerCase().includes(searchText.value.toLowerCase()),
  );
});

// 获取订单列表
async function fetchOrderList(paginationParams = pagination.value) {
  loading.value = true;
  try {
    const res = await getOrderInfoPage({
      pageNo: paginationParams.current,
      pageSize: paginationParams.pageSize,
    });
    orderList.value = res.list || [];
    pagination.value.total = res.total;
    pagination.value.current = paginationParams.current;
    pagination.value.pageSize = paginationParams.pageSize;
  } catch (error) {
    console.error('获取订单列表失败:', error);
    message.error('获取订单列表失败');
    orderList.value = [];
  } finally {
    loading.value = false;
  }
}

// 检查订单是否已绑定
function isBound(orderNo: string): boolean {
  return props.boundOrderNos.includes(orderNo);
}

// 绑定订单（单个）
async function handleBindOrder(orderNo: string) {
  await handleBatchBindOrders([orderNo]);
}

// 批量绑定订单
async function handleBatchBindOrders(orderNos: string[]) {
  // 验证必要参数
  if (!props.selectedBillNo || props.selectedBillNo.trim() === '') {
    message.warning('请先选择提单');
    return;
  }

  if (!orderNos || orderNos.length === 0) {
    message.warning('请选择要绑定的订单');
    return;
  }

  // 检查是否有已绑定的订单
  const unboundOrders = orderNos.filter((orderNo) => !isBound(orderNo));

  if (unboundOrders.length === 0) {
    message.warning('选中的订单都已绑定');
    return;
  }

  if (orderNos.length === 1) {
    bindingOrderNo.value = orderNos[0]!;
  } else {
    batchBinding.value = true;
  }

  try {
    // 收集费用数据
    const chargesData = unboundOrders.map(
      (orderNo) => orderOutstandingCharges.value[orderNo],
    );

    // 检查是否有实际的费用数据（非0且非undefined）
    const hasValidCharges = chargesData.some((charge) => charge && charge > 0);

    if (hasValidCharges) {
      // 有有效费用时，传递费用参数
      const outstandingCharges = chargesData.map((charge) => charge || 0);
      await batchBindOrdersToBill(
        props.selectedBillNo,
        unboundOrders,
        outstandingCharges,
      );
    } else {
      // 没有有效费用时，不传递费用参数
      await batchBindOrdersToBill(props.selectedBillNo, unboundOrders);
    }

    const successMsg =
      unboundOrders.length === 1
        ? '绑定成功'
        : `成功绑定 ${unboundOrders.length} 个订单`;
    message.success(successMsg);

    // 清空选择
    selectedRowKeys.value = [];
    emit('bindSuccess');
  } catch (error) {
    console.error('绑定失败:', error);
    message.error('绑定失败');
  } finally {
    bindingOrderNo.value = '';
    batchBinding.value = false;
  }
}

// 批量绑定选中的订单
function handleBatchBind() {
  handleBatchBindOrders(selectedRowKeys.value);
}

// 表格行选择配置
const rowSelection = {
  selectedRowKeys,
  onChange: (keys: string[]) => {
    selectedRowKeys.value = keys;
  },
  getCheckboxProps: (record: OrderInfoApi.OrderInfoVO) => ({
    disabled: isBound(record.orderNo) || !props.selectedBillNo,
  }),
};

// 表格变化处理
function onTableChange(paginationParams: any) {
  fetchOrderList(paginationParams);
}

// 搜索处理
function handleSearch() {
  // 搜索是通过计算属性实现的，这里可以添加额外逻辑
}

// 初始化
onMounted(() => {
  fetchOrderList();
});
</script>

<template>
  <a-card size="small" title="订单列表">
    <template #extra>
      <a-input-search
        v-model:value="searchText"
        placeholder="搜索订单号"
        style="width: 200px"
        @search="handleSearch"
        allow-clear
      />
    </template>

    <!-- 批量操作栏 -->
    <div v-if="selectedRowKeys.length > 0" class="mb-4 rounded border p-3">
      <div class="flex items-center justify-between">
        <span class="text-sm text-gray-600">
          已选择 {{ selectedRowKeys.length }} 个订单
        </span>
        <div class="flex gap-2">
          <a-button size="small" @click="selectedRowKeys = []">
            取消选择
          </a-button>
          <a-button
            type="primary"
            size="small"
            :loading="batchBinding"
            :disabled="!props.selectedBillNo"
            @click="handleBatchBind"
          >
            批量绑定
          </a-button>
        </div>
      </div>
    </div>

    <a-table
      row-key="orderNo"
      :columns="columns"
      :data-source="filteredOrderList"
      :pagination="pagination"
      :loading="loading"
      :row-selection="rowSelection"
      @change="onTableChange"
      bordered
      size="small"
      :scroll="{ y: 400 }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'outstandingCharges'">
          <a-input-number
            v-model:value="orderOutstandingCharges[record.orderNo]"
            :min="0"
            :precision="2"
            placeholder="可选"
            size="small"
            style="width: 120px"
            :disabled="isBound(record.orderNo)"
          />
        </template>
        <template v-if="column.key === 'action'">
          <a-button
            type="primary"
            size="small"
            :disabled="isBound(record.orderNo) || !props.selectedBillNo"
            :loading="bindingOrderNo === record.orderNo"
            @click="handleBindOrder(record.orderNo)"
          >
            {{ isBound(record.orderNo) ? '已绑定' : '绑定' }}
          </a-button>
        </template>
      </template>
    </a-table>
  </a-card>
</template>
