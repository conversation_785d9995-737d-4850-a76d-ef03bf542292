<script lang="ts" setup>
import { message } from 'ant-design-vue';

function handleChange(info: any) {
  if (info.file.status === 'done') {
    message.success('导入成功');
    // 可 emit success 事件
  }
}
</script>
<template>
  <a-upload
    action="/logistics/shipping-bill/import-excel"
    :show-upload-list="false"
    @change="handleChange"
  >
    <a-button type="primary">导入提单信息</a-button>
  </a-upload>
  <a href="/logistics/shipping-bill/export-excel-template" target="_blank">
    下载导入模板
  </a>
</template>
