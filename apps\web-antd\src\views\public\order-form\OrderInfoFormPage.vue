<script lang="ts" setup>





import { nextTick, onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import { Button, message } from 'ant-design-vue';
// @ts-ignore
// eslint-disable-next-line n/no-extraneous-import
import html2pdf from 'html2pdf.js';
// @ts-ignore
// @ts-ignore
// @ts-ignore
// @ts-ignore
// eslint-disable-next-line n/no-extraneous-import
import JsBarcode from 'jsbarcode';

import { useVbenForm } from '#/adapter/form';
import { getAgentQueryImages } from '#/api/logistics/agentinfo';
import { createOrderInfo } from '#/api/logistics/orderinfo';
import { ImageUpload } from '#/components/upload';
import { $t } from '#/locales';

// @ts-ignore
import { updateMarkNumberAndWarehouse, useFormSchema } from './data';
// @ts-ignore
// @ts-ignore
import OrderItemGridForm from './OrderItemGridForm.vue';

// 路由相关
const route = useRoute();

// 代理相关
const agentName = ref<string>('');
const agentLogo = ref<string>('');
const adImages = ref<string[]>([]);

// 图片上传相关
const imgUrl = ref<string>('');

// 默认logo
const defaultLogo = '';

const [Form, formApi] = useVbenForm({
  commonConfig: {
    componentProps: { class: 'w-full' },
    formItemClass: 'col-span-1',
    labelWidth: 100,
  },
  layout: 'horizontal',
  schema: useFormSchema(),
  showDefaultActions: true,
  handleSubmit,
  wrapperClass: 'grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-2',
});

declare global {
  interface Window {
    __vbenFormApi: any;
    __vbenMarkNumberRef: any;
    __vbenMarkNumberQrRef: any;
    __vbenOrderNoQrRef: any;
    __vbenAgentName: string;
  }
}

const orderItemGridRef = ref();
// 打印按钮可用状态
const canPrint = ref(false);
const markNumber = ref('');
const orderNoQrRef = ref('');
const markNumberQrRef = ref('');
const markNumberBarcodeRef = ref<HTMLCanvasElement>();
const orderNoBarcodeRef = ref<HTMLCanvasElement>();

// 打印模板相关（已废弃，使用Excel模板）
// const showPrintTemplate = ref(false);
// const printTemplateRef = ref();
// const currentFormData = ref({});
// const currentOrderItems = ref([]);
// 生成唛头条形码
function generateBarcode(text: string) {
  if (markNumberBarcodeRef.value && text) {
    try {
      JsBarcode(markNumberBarcodeRef.value, text, {
        format: "CODE128",
        width: 1.5,
        height: 40,
        displayValue: false,
        fontSize: 8,
        margin: 3,
        background: "transparent"
      });
    } catch (error) {
      console.error('生成唛头条形码失败:', error);
    }
  }
}

// 生成单号条形码
function generateOrderNoBarcode(text: string) {
  if (orderNoBarcodeRef.value && text) {
    try {
      JsBarcode(orderNoBarcodeRef.value, text, {
        format: "CODE128",
        width: 1.5,
        height: 40,
        displayValue: false,
        fontSize: 8,
        margin: 3,
        background: "transparent"
      });
    } catch (error) {
      console.error('生成单号条形码失败:', error);
    }
  }
}

// 挂载formApi和markNumber到window，供data.ts onChange使用
function setupGlobalVariables() {
  if (typeof window !== 'undefined') {
    window.__vbenFormApi = formApi;
    window.__vbenMarkNumberRef = markNumber;
    window.__vbenMarkNumberQrRef = markNumberQrRef;
    window.__vbenOrderNoQrRef = orderNoQrRef;
    window.__vbenAgentName = '';
  }
}

// 保存草稿 key
const DRAFT_KEY = 'orderInfoDraft';

// 保存草稿方法
async function handleSaveDraft() {
  const values = await formApi.getValues();
  const items = orderItemGridRef.value?.getData() ?? [];
  const draft = { values, items };
  localStorage.setItem(DRAFT_KEY, JSON.stringify(draft));
  message.success('草稿已保存到本地');
}

// 是否可以保存草稿
const canSaveDraft = ref(true);

// 加载草稿方法
async function handleLoadDraft() {
  const draftStr = localStorage.getItem(DRAFT_KEY);
  if (!draftStr) {
    message.info('暂无草稿数据');
    return;
  }
  try {
    const draft = JSON.parse(draftStr);
    if (draft.values) {
      await formApi.setValues(draft.values);
    }
    if (draft.items && Array.isArray(draft.items)) {
      orderItemGridRef.value?.setData(draft.items);
    }
    message.success('草稿已加载');
  } catch (error) {
    console.error('加载草稿失败:', error);
    message.error('草稿数据格式错误');
  }
}

async function handleSubmit(values: any) {
  // 通过子控件获取明细数据
  const items = (orderItemGridRef.value?.getData() as any[]) ?? [];
  if (items.length === 0) {
    message.error('请录入装箱明细');
    return;
  }
  // 校验每一行商品名称
  for (const [i, item] of items.entries()) {
    if (!item.itemName || !item.itemName.trim()) {
      message.error(`第${i + 1}行商品名称不能为空`);
      return;
    }
  }
  if ('id' in values) delete values.id;
  // 合并主表和子表数据
  const payload = { ...values, orderItems: items };
  const res = await createOrderInfo(payload as any);
  // 返回ordreNO 填充表单后才能正式打印
  formApi.setValues({ orderNo: res });
  if (typeof window !== 'undefined' && window.__vbenOrderNoQrRef) {
    window.__vbenOrderNoQrRef.value = res;
  }
  // 主动赋值二维码内容
  orderNoQrRef.value = res || '';
  // 如果 markNumber 也需要二维码，主动赋值
  const markNumber = (values.markNumber || '').toString();
  markNumberQrRef.value = markNumber;
  // 新增：打印按钮可用状态
  canPrint.value = true;
  message.success($t('ui.actionMessage.operationSuccess'));
  // 提交成功后清除草稿
  localStorage.removeItem(DRAFT_KEY);
  // 提交成功后自动下载 PDF
  handleDownloadPDF();
  // 提交后禁用保存草稿按钮
  canSaveDraft.value = false;
}

function handleReset() {
  formApi.resetForm();
  orderItemGridRef.value?.setData([]);
  canPrint.value = false;
  localStorage.removeItem(DRAFT_KEY); // 清空草稿
  // 清空二维码内容
  orderNoQrRef.value = '';
  markNumberQrRef.value = '';
  // 重置后允许保存草稿
  canSaveDraft.value = true;
}
// 准备打印数据
// preparePrintData函数已废弃，使用Excel模板直接生成HTML

// 使用Excel模板打印
async function handlePrint() {
  try {
    const formValues = await formApi.getValues();
    const orderItems = orderItemGridRef.value?.getData() || [];

    // 检查必要数据
    if (!formValues.orderNo) {
      message.error('请先提交表单生成单号');
      return;
    }

    // 准备数据
    const excelData = {
      formData: {
        orderNo: formValues.orderNo,
        markNumber: formValues.markNumber,
        customerName: formValues.customerName,
        customerTel: formValues.customerTel,
        supplierName: formValues.supplierName,
        supplierTel: formValues.supplierTel,
        shippingMethod: formValues.shippingMethod,
        warehouse: formValues.warehouse,
        quantity: formValues.quantity,
        volume: formValues.volume,
        weight: formValues.weight,
        trackingNumber: formValues.trackingNumber,
        createTime: formValues.createTime,
      },
      orderItems: orderItems.map((item: { itemName: any; quantity: any; }) => ({
        itemName: item.itemName,
        quantity: item.quantity,
      })),
      logoUrl: agentLogo.value,
      currentDate: new Date().toLocaleDateString('zh-CN'),
      warehouseCode: 'clubfs.warehouse101.cn/#',
      serialNumber: '679',
    };

    // 动态导入并生成HTML
    const { convertExcelTemplateToHTML } = await import('#/utils/excel-template');
    const htmlContent = await convertExcelTemplateToHTML(excelData);

    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      message.error('无法打开打印窗口，请检查浏览器弹窗设置');
      return;
    }

    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.focus();

    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 500);

  } catch (error) {
    console.error('打印失败:', error);
    message.error('打印失败，请重试');
  }
}

// 使用Excel模板下载PDF
async function handleDownloadPDF() {
  try {
    const formValues = await formApi.getValues();
    const orderItems = orderItemGridRef.value?.getData() || [];

    // 检查必要数据
    if (!formValues.orderNo) {
      message.error('请先提交表单生成单号');
      return;
    }

    // 准备数据
    const excelData = {
      formData: {
        orderNo: formValues.orderNo,
        markNumber: formValues.markNumber,
        customerName: formValues.customerName,
        customerTel: formValues.customerTel,
        supplierName: formValues.supplierName,
        supplierTel: formValues.supplierTel,
        shippingMethod: formValues.shippingMethod,
        warehouse: formValues.warehouse,
        quantity: formValues.quantity,
        volume: formValues.volume,
        weight: formValues.weight,
        trackingNumber: formValues.trackingNumber,
        createTime: formValues.createTime,
      },
      orderItems: orderItems.map((item: { itemName: any; quantity: any; }) => ({
        itemName: item.itemName,
        quantity: item.quantity,
      })),
      logoUrl: agentLogo.value,
      currentDate: new Date().toLocaleDateString('zh-CN'),
      warehouseCode: 'clubfs.warehouse101.cn/#',
      serialNumber: '679',
    };

    // 动态导入并生成HTML
    const { convertExcelTemplateToHTML } = await import('#/utils/excel-template');
    const htmlContent = await convertExcelTemplateToHTML(excelData);

    // 创建临时DOM元素
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;
    tempDiv.style.position = 'absolute';
    tempDiv.style.left = '-9999px';
    tempDiv.style.top = '-9999px';
    document.body.append(tempDiv);

    const opt = {
      margin: 0,
      filename: `delivery-note-${formValues.orderNo || Date.now()}.pdf`,
      image: { type: 'jpeg', quality: 0.98 },
      html2canvas: {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
      },
      jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' },
    };

    await html2pdf().set(opt).from(tempDiv).save();

    // 清理临时元素
    tempDiv.remove();

  } catch (error) {
    console.error('PDF生成失败:', error);
    message.error('PDF生成失败，请重试');
  }
}



// 加载代理信息
async function loadAgentInfo(agentName: string) {
  try {
    const agentInfo = await getAgentQueryImages(agentName);

    if (agentInfo) {
      // 更新 logo
      agentLogo.value = agentInfo.agentLogo || defaultLogo;

      // 更新广告图片
      const images = [
        agentInfo.image1Url,
        agentInfo.image2Url,
      ].filter(Boolean) as string[];

      if (images.length > 0) {
        adImages.value = images;
      }
    } else {
      // 没有找到代理信息，使用默认值
      agentLogo.value = defaultLogo;
    }
  } catch (error) {
    console.warn('加载代理信息失败:', error);
    // 使用默认图片
    agentLogo.value = defaultLogo;
  }
}

// 初始化代理信息
async function initializeAgentInfo() {
  const routeAgentName = route.params.agentName as string;

  if (routeAgentName) {
    agentName.value = routeAgentName;
    // 设置代理名称到全局变量
    if (typeof window !== 'undefined') {
      window.__vbenAgentName = routeAgentName;
    }
    await loadAgentInfo(routeAgentName);
  } else {
    // 没有代理参数时，使用默认logo
    agentLogo.value = defaultLogo;
    if (typeof window !== 'undefined') {
      window.__vbenAgentName = '';
    }
  }

  // 初始化完成后，更新唛头和入库仓库
  setTimeout(() => {
    updateMarkNumberAndWarehouse();
  }, 200);
}

// 监听路由参数变化
watch(() => route.params.agentName, async (newAgentName) => {
  if (newAgentName && typeof newAgentName === 'string') {
    agentName.value = newAgentName;
    // 设置代理名称到全局变量
    if (typeof window !== 'undefined') {
      window.__vbenAgentName = newAgentName;
    }
    await loadAgentInfo(newAgentName);
    // 更新唛头和入库仓库
    setTimeout(() => {
      updateMarkNumberAndWarehouse();
    }, 200);
  } else {
    if (typeof window !== 'undefined') {
      window.__vbenAgentName = '';
    }
  }
}, { immediate: true });

// 创建响应式的表单值
const customerTel = ref('');
const shippingMethod = ref(null);

// 监听表单值变化，实现联动
function setupFormWatchers() {
  // 监听客户电话变化
  watch(customerTel, (newTel) => {
    if (newTel) {
      updateMarkNumberFromTel(newTel);
    }
  });

  // 监听运输方式变化
  watch(shippingMethod, () => {
    setTimeout(() => {
      updateMarkNumberAndWarehouse();
    }, 100);
  });
}

// 手动同步表单值到响应式变量
function syncFormValues() {
  const interval = setInterval(async () => {
    try {
      const values = await formApi.getValues();
      customerTel.value = values.customerTel || '';
      shippingMethod.value = values.shippingMethod || null;
      // 同步图片值
      if (values.imgUrl !== imgUrl.value) {
        imgUrl.value = values.imgUrl || '';
      }
    } catch {
      // 表单还未初始化完成
    }
  }, 500);

  // 10秒后停止同步（表单应该已经稳定）
  setTimeout(() => {
    clearInterval(interval);
  }, 10_000);
}

// 根据电话号码更新唛头
async function updateMarkNumberFromTel(tel: string) {
  const digits = [...(tel || '')].filter((char: string) => /\d/.test(char)).join('');
  const baseMarkNumber = digits.slice(-5);

  if (baseMarkNumber) {
    try {
      const currentValues = await formApi.getValues();
      const currentShippingMethod = currentValues.shippingMethod;

      // 根据运输方式拼接后缀
      let markNumber = baseMarkNumber;
      if (currentShippingMethod === 1) {
        markNumber = `${baseMarkNumber}-AIR`;
      } else if (currentShippingMethod === 2) {
        markNumber = `${baseMarkNumber}-SEA`;
      }

      formApi.setFieldValue('markNumber', markNumber);
      markNumberQrRef.value = markNumber;

      // 同时更新入库仓库
      updateWarehouse(currentShippingMethod);
    } catch (error) {
      console.error('更新唛头失败:', error);
    }
  }
}

// 更新入库仓库
function updateWarehouse(shippingMethod: number) {
  const routeAgentName = route.params.agentName as string;
  if (routeAgentName) {
    let warehouse = routeAgentName;
    if (shippingMethod === 1) {
      warehouse = `${routeAgentName}-AIR`;
    } else if (shippingMethod === 2) {
      warehouse = `${routeAgentName}-SEA`;
    }

    try {
      formApi.setFieldValue('warehouse', warehouse);
    } catch (error) {
      console.error('更新入库仓库失败:', error);
    }
  }
}

// 监听唛头变化，生成条形码
watch(markNumberQrRef, (newValue) => {
  if (newValue) {
    nextTick(() => {
      generateBarcode(newValue);
    });
  }
});

// 监听单号变化，生成条形码
watch(orderNoQrRef, (newValue) => {
  if (newValue) {
    nextTick(() => {
      generateOrderNoBarcode(newValue);
    });
  }
});

// 监听图片值变化，同步到表单
watch(imgUrl, (newValue) => {
  try {
    formApi.setFieldValue('imgUrl', newValue);
  } catch (error) {
    console.error('同步图片值到表单失败:', error);
  }
});

// 强制修复 Ant Design 的固定尺寸问题
function fixUploadContainerSize() {
  nextTick(() => {
    // 修复所有相关的固定尺寸元素
    const selectors = [
      '.image-upload-full .ant-upload-list-item-container',
      '.image-upload-full .ant-upload-select-picture-card',
      '.image-upload-full .ant-upload-select',
      '.image-upload-full .ant-upload'
    ];

    selectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        if (element instanceof HTMLElement) {
          // 强制设置内联样式覆盖固定尺寸
          element.style.setProperty('width', '100%', 'important');
          element.style.setProperty('height', '100%', 'important');
          element.style.setProperty('min-width', '100%', 'important');
          element.style.setProperty('min-height', '100%', 'important');
          element.style.setProperty('max-width', 'none', 'important');
          element.style.setProperty('max-height', 'none', 'important');
          element.style.setProperty('position', 'absolute', 'important');
          element.style.setProperty('top', '0', 'important');
          element.style.setProperty('left', '0', 'important');
          element.style.setProperty('right', '0', 'important');
          element.style.setProperty('bottom', '0', 'important');
          element.style.setProperty('margin', '0', 'important');
          element.style.setProperty('padding', '0', 'important');
          element.style.setProperty('border-radius', '8px', 'important');

          // 特殊处理上传按钮
          if (element.classList.contains('ant-upload-select-picture-card')) {
            element.style.setProperty('display', 'flex', 'important');
            element.style.setProperty('align-items', 'center', 'important');
            element.style.setProperty('justify-content', 'center', 'important');
            element.style.setProperty('border', '2px dashed #d9d9d9', 'important');
            element.style.setProperty('background', '#fafafa', 'important');
            element.style.setProperty('cursor', 'pointer', 'important');
          }
        }
      });
    });
  });
}

// 监听图片上传状态变化，修复容器尺寸
watch(imgUrl, () => {
  fixUploadContainerSize();
});

// 组件挂载后修复尺寸
onMounted(() => {
  fixUploadContainerSize();

  // 使用 MutationObserver 监听 DOM 变化
  const observer = new MutationObserver(() => {
    fixUploadContainerSize();
  });

  // 监听图片上传区域的变化
  const uploadArea = document.querySelector('.image-upload-full');
  if (uploadArea) {
    observer.observe(uploadArea, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class', 'style']
    });
  }

  // 定期检查并修复（备用方案）
  const interval = setInterval(fixUploadContainerSize, 500);
  setTimeout(() => {
    clearInterval(interval);
    observer.disconnect();
  }, 15_000);
});

onMounted(() => {
  setupGlobalVariables();
  initializeAgentInfo();
  // 延迟设置监听器，确保表单已完全初始化
  setTimeout(() => {
    setupFormWatchers();
    syncFormValues();
  }, 500);
});
</script>

<template>
  <div class="order-form-page">
    <!-- 背景容器 -->
    <div class="bg-container">
      <!-- 主要内容区域 -->
      <div class="content-wrapper">
        <!-- Logo 区域 -->
        <div class="logo-section">
          <img :src="agentLogo || defaultLogo" alt="Agent Logo" class="agent-logo" />
        </div>

        <!-- 内容区域 - 包含左右广告位 -->
        <div class="main-content-area">
          <!-- 左侧广告位 -->
          <div class="left-ad-section">
            <div v-if="adImages[0]" class="ad-item">
              <img :src="adImages[0]" alt="广告 1" class="ad-image" />
            </div>
          </div>

          <!-- 中间表单区域 -->
        <div
          class="relative z-10 flex w-full max-w-4xl flex-col justify-start rounded-2xl border border-gray-200 bg-white/90 p-8 text-lg shadow-2xl md:text-xl print-area"
        >
          <!-- 标题区域 -->
          <div class="mb-4 w-full relative">
            <!-- 单号条形码 - 右上角，给标题留出空间 -->
            <div class="absolute top-0 right-0" style="margin-top: -20px;">
              <canvas
                ref="orderNoBarcodeRef"
                v-if="orderNoQrRef"
                class="barcode-canvas order-no-barcode"
              ></canvas>
            </div>

            <!-- 标题和副标题 - 居中 -->
            <div class="text-center">
              <div
                style="
                  font-size: 3rem;
                  font-weight: bold;
                  color: black;
                  letter-spacing: 2px;
                "
              >
                Delivery Note
              </div>
              <div
                style="
                  font-size: 1.1rem;
                  font-weight: bold;
                  color: #e53935;
                  margin-top: 1.45rem;
                  letter-spacing: 1px;
                "
              >
                送货前请先联系 Sean 18188802770( 微信）填写此单贴在外包装上
              </div>
            </div>
          </div>
          <!-- 子表移到Form内部，二维码区、Grid、按钮区、注意事项区均 col-span-full -->
          <Form @submit="handleSubmit">
<template #markNumberQr>
              <div class="col-span-full mb-2 flex items-center justify-center">
                <canvas
                  ref="markNumberBarcodeRef"
                  v-if="markNumberQrRef"
                  class="barcode-canvas"
                ></canvas>
              </div>
            </template>
            <!-- 其余表单项自动渲染 -->
            <!-- 子表和图片区域并排显示 -->
            <div class="col-span-full mb-4">
              <div class="flex gap-4">
                <!-- 左侧子表区域 -->
                <div class="flex-1">
                  <OrderItemGridForm
                    ref="orderItemGridRef"
                    style="min-width: 100px; width: 100%"
                  />
                </div>
                <!-- 右侧图片区域 -->
                <div class="flex-1">
                  <div class="border border-gray-200 rounded-lg h-full overflow-hidden relative">
                    <!-- 图片上传组件 -->
                    <ImageUpload
                      v-model:value="imgUrl"
                      :max-number="1"
                      :file-size="5"
                      :file-type="['image/jpeg', 'image/jpg', 'image/png', 'image/gif']"
                      :show-description="false"
                      class="image-upload-full absolute inset-0"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- 图片字段插槽 -->
            <template #imgUrl>
              <div class="hidden">
                <!-- 隐藏原始的图片字段，我们会在上面的区域手动渲染 -->
              </div>
            </template>
            <div class="no-print col-span-full mb-4">
              <div class="flex justify-end gap-4">
                <Button
                  native-type="button"
                  type="primary"
                  ghost
                  class="rounded shadow"
                  @click="orderItemGridRef.onAdd()"
                >
                  新增装箱明细
                </Button>
                <Button
                  native-type="button"
                  type="default"
                  class="rounded shadow"
                  @click="handleSaveDraft"
                  :disabled="!canSaveDraft"
                >
                  保存草稿
                </Button>
                <Button
                  native-type="button"
                  type="default"
                  class="rounded shadow"
                  @click="handleLoadDraft"
                >
                  加载草稿
                </Button>
                <Button
                  @click="handleReset"
                  type="default"
                  class="rounded shadow"
                >
                  重置
                </Button>
                <Button
                  html-type="submit"
                  type="primary"
                  class="rounded shadow"
                >
                  提交
                </Button>

                <Button
                  @click="handleDownloadPDF"
                  type="primary"
                  class="rounded shadow"
                  :disabled="!canPrint"
                >
                  下载PDF
                </Button>
                <Button
                  @click="handlePrint"
                  type="primary"
                  class="rounded shadow"
                  :disabled="!canPrint"
                >
                  打印
                </Button>
              </div>
            </div>
          </Form>

          <!-- 注意事项区域 -->
          <div class="no-print col-span-full mt-6">
            <div class="rounded-lg bg-yellow-50 p-4">
              <h3 class="mb-2 text-lg font-semibold text-yellow-800">
                注意事项：
              </h3>
              <ul class="space-y-1 pl-4 text-sm leading-tight text-gray-800">
                  <li>
                    Warehouse address 仓库地址 :
                    佛山顺德区乐从镇新隆村第一工业区内 101
                  </li>
                  <li>Sean: +8618188802770</li>
                  <li>上班时间：周一到周六，上午 10 点到下午 6 点。</li>
                  <li>本仓库不收到付件，不收无唛头的件。</li>
                  <li>仓库没有免费卸货服务，如需卸货，请联系仓库咨询费用。</li>
                </ul>
            </div>
          </div>
        </div>

          <!-- 右侧广告位 -->
          <div class="right-ad-section">
            <div v-if="adImages[1]" class="ad-item">
              <img :src="adImages[1]" alt="广告 2" class="ad-image" />
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 简单的页脚 -->
    <footer class="public-footer">
      <div class="footer-content">
        <p>&copy; 2025 物流管理系统. All rights reserved.</p>
      </div>
    </footer>
  </div>
</template>

<style lang="scss" scoped>
.order-form-page {
  min-height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;
}

.bg-container {
  flex: 1;
  background: url('/bj1.jpg') no-repeat center center;
  background-size: cover;
  background-attachment: fixed;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 0;
  }
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

.logo-section {
  margin-bottom: 20px;
  text-align: center;

  .agent-logo {
    max-height: 80px;
    max-width: 200px;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.main-content-area {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  width: 100%;
  max-width: 1400px;
}

.left-ad-section,
.right-ad-section {
  flex: 0 0 200px;
  display: flex;
  flex-direction: column;
  gap: 20px;

  .ad-item {
    .ad-image {
      width: 100%;
      height: auto;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }
    }
  }
}



.public-footer {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);

  .footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 10px 20px;
    text-align: center;

    p {
      margin: 0;
      color: #6b7280;
      font-size: 14px;
    }
  }
}

// 打印样式
@media print {
  .no-print {
    display: none !important;
  }

  .order-form-page {
    background: white !important;
  }

  .bg-container {
    background: white !important;

    &::before {
      display: none !important;
    }
  }

  .content-wrapper {
    padding: 0 !important;
    min-height: auto !important;
  }

  .public-footer {
    display: none !important;
  }
}

.barcode-canvas {
  background: transparent;
  padding: 2px;
  border: none;
}

/* 图片上传区域样式 - 完全重写以确保占满 */
.image-upload-full {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
  border-radius: 8px !important;
  overflow: hidden !important;
}

/* 重写 ImageUpload 组件的根容器 */
.image-upload-full > div {
  width: 100% !important;
  height: 100% !important;
  position: relative !important;
}

/* 强制重写所有 Upload 相关容器 */
.image-upload-full :deep(.ant-upload),
.image-upload-full :deep(.ant-upload-wrapper),
.image-upload-full :deep(.ant-upload-list),
.image-upload-full :deep(.ant-upload-list-picture-card) {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}

/* 上传按钮区域 - 完全占满 */
.image-upload-full :deep(.ant-upload-select),
.image-upload-full :deep(.ant-upload-select-picture-card) {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
  border-radius: 8px !important;
  margin: 0 !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border: 2px dashed #d9d9d9 !important;
  background: #fafafa !important;
  cursor: pointer !important;
}

/* 已上传的图片项 - 完全重写 */
.image-upload-full :deep(.ant-upload-list-item),
.image-upload-full :deep(.ant-upload-list-picture-card .ant-upload-list-item) {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  border-radius: 8px !important;
  background: transparent !important;
}

/* 图片缩略图容器 - 完全占满 */
.image-upload-full :deep(.ant-upload-list-item-thumbnail),
.image-upload-full :deep(.ant-upload-list-picture-card .ant-upload-list-item-thumbnail) {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
  background: transparent !important;
}

/* 实际图片 - 完全铺满 */
.image-upload-full :deep(.ant-upload-list-item-thumbnail img),
.image-upload-full :deep(.ant-upload-list-picture-card .ant-upload-list-item-thumbnail img) {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  border-radius: 8px !important;
  border: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 操作按钮 */
.image-upload-full :deep(.ant-upload-list-picture-card .ant-upload-list-item-actions) {
  position: absolute !important;
  top: 8px !important;
  right: 8px !important;
  background: rgba(0, 0, 0, 0.6) !important;
  border-radius: 4px !important;
  padding: 4px 8px !important;
  z-index: 10 !important;
}

/* 操作按钮图标 */
.image-upload-full :deep(.ant-upload-list-picture-card .ant-upload-list-item-actions .anticon) {
  color: white !important;
  font-size: 16px !important;
}

/* 隐藏进度条 */
.image-upload-full :deep(.ant-upload-list-picture-card .ant-upload-list-item-progress) {
  display: none !important;
}

/* 当有图片时，隐藏上传按钮区域 */
.image-upload-full :deep(.ant-upload-list-picture-card:has(.ant-upload-list-item) .ant-upload-select-picture-card) {
  display: none !important;
}

/* 鼠标悬停时显示操作按钮 */
.image-upload-full :deep(.ant-upload-list-picture-card .ant-upload-list-item-actions) {
  opacity: 0 !important;
  transition: opacity 0.3s ease !important;
}

.image-upload-full :deep(.ant-upload-list-picture-card .ant-upload-list-item:hover .ant-upload-list-item-actions) {
  opacity: 1 !important;
}

/* 上传按钮悬停效果 */
.image-upload-full :deep(.ant-upload-select-picture-card:hover) {
  border-color: #1890ff !important;
  background: #f0f8ff !important;
}

/* 强制移除所有可能的间距和边框 */
.image-upload-full :deep(*) {
  box-sizing: border-box !important;
}

.image-upload-full :deep(.ant-upload-list-item-info) {
  display: none !important;
}

.image-upload-full :deep(.ant-upload-list-item-name) {
  display: none !important;
}

/* 确保没有任何默认的 margin 或 padding */
.image-upload-full :deep(.ant-upload-list-picture-card .ant-upload-list-item) {
  float: none !important;
  display: block !important;
}

/* 强制图片占满整个空间 */
.image-upload-full :deep(img) {
  max-width: none !important;
  max-height: none !important;
  min-width: 100% !important;
  min-height: 100% !important;
}

/* 覆盖 Ant Design 的固定尺寸样式 - 使用更高优先级的选择器 */
.image-upload-full :deep(.ant-upload-wrapper.ant-upload-picture-card-wrapper .ant-upload-list.ant-upload-list-picture-card .ant-upload-list-item-container) {
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 覆盖具体的 picture-card 容器 */
.image-upload-full :deep(.ant-upload-picture-card-wrapper) {
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
}

/* 覆盖 picture-card 列表项 */
.image-upload-full :deep(.ant-upload-list-picture-card .ant-upload-list-item-container) {
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  float: none !important;
  display: block !important;
}

/* 使用更高优先级的选择器覆盖所有可能的固定尺寸 */
.image-upload-full.image-upload-full :deep(.ant-upload-wrapper.ant-upload-picture-card-wrapper .ant-upload-list.ant-upload-list-picture-card .ant-upload-list-item-container),
.image-upload-full.image-upload-full :deep(.ant-upload-list-picture-card .ant-upload-list-item-container),
.image-upload-full.image-upload-full :deep(.ant-upload-list-item-container) {
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  float: none !important;
  display: block !important;
}

/* 覆盖上传选择区域的固定尺寸 */
.image-upload-full.image-upload-full :deep(.ant-upload-select-picture-card) {
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 最高优先级样式 - 使用 CSS 层叠和特异性来强制覆盖 */
.image-upload-full[class*="image-upload-full"] :deep([class*="ant-upload"]) {
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

.image-upload-full[class*="image-upload-full"] :deep([class*="ant-upload-list-item"]) {
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

.image-upload-full[class*="image-upload-full"] :deep([class*="ant-upload-select"]) {
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 针对具体的 CSS 类名进行覆盖 */
.image-upload-full :deep([class*="css-"][class*="ant-upload-wrapper"][class*="ant-upload-picture-card-wrapper"] [class*="ant-upload-list"][class*="ant-upload-list-picture-card"] [class*="ant-upload-list-item-container"]) {
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 专门针对所有固定尺寸的强制覆盖 */
.image-upload-full :deep(.ant-upload-list-item-container),
.image-upload-full :deep(.ant-upload-select-picture-card),
.image-upload-full :deep(.ant-upload-select),
.image-upload-full :deep(.ant-upload) {
  width: 100% !important;
  height: 100% !important;
  min-width: 100% !important;
  min-height: 100% !important;
  max-width: none !important;
  max-height: none !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  float: none !important;
  display: block !important;
}

/* 特殊处理上传按钮的显示方式 */
.image-upload-full :deep(.ant-upload-select-picture-card) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border: 2px dashed #d9d9d9 !important;
  background: #fafafa !important;
  cursor: pointer !important;
  border-radius: 8px !important;
}

/* 使用更高权重的选择器强制覆盖所有固定尺寸 */
.image-upload-full.absolute.inset-0 :deep(.ant-upload-list-item-container),
.image-upload-full.absolute.inset-0 :deep(.ant-upload-select-picture-card),
.image-upload-full.absolute.inset-0 :deep(.ant-upload-select),
.image-upload-full.absolute.inset-0 :deep(.ant-upload),
.image-upload-full.absolute.inset-0 :deep([class*="ant-upload-list-item-container"]),
.image-upload-full.absolute.inset-0 :deep([class*="ant-upload-select-picture-card"]),
.image-upload-full.absolute.inset-0 :deep([class*="ant-upload-select"]),
.image-upload-full.absolute.inset-0 :deep([class*="ant-upload"]) {
  width: 100% !important;
  height: 100% !important;
  min-width: 100% !important;
  min-height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  box-sizing: border-box !important;
}

/* 使用 CSS 自定义属性强制覆盖 */
.image-upload-full {
  --ant-upload-item-width: 100% !important;
  --ant-upload-item-height: 100% !important;
}

.image-upload-full :deep(.ant-upload-list-item-container) {
  width: var(--ant-upload-item-width) !important;
  height: var(--ant-upload-item-height) !important;
}

/* 图片上传容器样式 */
.image-upload-full {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

/* 让图片区域高度与子表保持一致 */
.flex.gap-4 {
  align-items: stretch !important;
}

.flex.gap-4 > .flex-1 {
  display: flex !important;
  flex-direction: column !important;
}

.flex.gap-4 > .flex-1 > div {
  flex: 1 !important;
  height: 100% !important;
}

/* 右上角单号条形码样式 */
.order-no-barcode {
  background: transparent !important;
}

/* 确保单号条形码在最右上角 */
.absolute.-top-2.-right-2 {
  z-index: 10;
}

// 响应式设计
@media (max-width: 768px) {
  .content-wrapper {
    padding: 20px 16px;
  }

  .main-content-area {
    flex-direction: column;
    align-items: center;
  }

  .left-ad-section,
  .right-ad-section {
    display: none; // 在移动端隐藏广告位
  }
}
</style>
