import request from '@/config/axios'

// 入库装箱 VO
export interface OrderItemVO {
  id: number // 主键
  boxNumber: string // 装箱单
  itemNumber: string // 装箱单
  itemName: string // 商品名称
  quantity: number // 商品数量
}

// 入库装箱 API
export const OrderItemApi = {
  // 查询入库装箱分页
  getOrderItemPage: async (params: any) => {
    return await request.get({ url: `/logistics/order-item/page`, params })
  },

  // 查询入库装箱详情
  getOrderItem: async (id: number) => {
    return await request.get({ url: `/logistics/order-item/get?id=` + id })
  },

  // 新增入库装箱
  createOrderItem: async (data: OrderItemVO) => {
    return await request.post({ url: `/logistics/order-item/create`, data })
  },

  // 修改入库装箱
  updateOrderItem: async (data: OrderItemVO) => {
    return await request.put({ url: `/logistics/order-item/update`, data })
  },

  // 删除入库装箱
  deleteOrderItem: async (id: number) => {
    return await request.delete({ url: `/logistics/order-item/delete?id=` + id })
  },

  // 导出入库装箱 Excel
  exportOrderItem: async (params) => {
    return await request.download({ url: `/logistics/order-item/export-excel`, params })
  }
}