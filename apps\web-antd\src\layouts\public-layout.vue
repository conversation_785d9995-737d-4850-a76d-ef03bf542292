<template>
  <div class="public-layout">
    <!-- 简单的顶部导航 -->
    <header class="public-header">
      <div class="header-content">
        <div class="logo-section">
          <h1 class="logo-text">物流管理系统</h1>
        </div>
        <div class="nav-section">
          <a-button type="link" @click="goToLogin">
            管理员登录
          </a-button>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="public-main">
      <router-view />
    </main>

    <!-- 简单的页脚 -->
    <footer class="public-footer">
      <div class="footer-content">
        <p>&copy; 2025 物流管理系统. All rights reserved.</p>
      </div>
    </footer>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router';

const router = useRouter();

function goToLogin() {
  router.push('/auth/login');
}
</script>

<style lang="scss" scoped>
.public-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.public-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
  
  .header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .logo-section {
      .logo-text {
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
      }
    }
    
    .nav-section {
      display: flex;
      align-items: center;
      gap: 16px;
    }
  }
}

.public-main {
  flex: 1;
  position: relative;
}

.public-footer {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  
  .footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    text-align: center;
    
    p {
      margin: 0;
      color: #6b7280;
      font-size: 14px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
    
    .logo-text {
      font-size: 18px;
    }
  }
  
  .footer-content {
    padding: 16px;
  }
}
</style>
