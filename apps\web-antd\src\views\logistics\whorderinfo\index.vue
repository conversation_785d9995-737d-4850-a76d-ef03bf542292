<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { WhOrderInfoApi } from '#/api/logistics/whorderinfo';
import type { ActionItem } from '#/components/table-action/typing';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromBlobPart, isEmpty } from '@vben/utils';

import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

import { ACTION_ICON, TableAction, useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteWhOrderInfo,
  exportWhOrderInfo,
  getWhOrderInfoPage,
} from '#/api/logistics/whorderinfo';
import { $t } from '#/locales';

import { useGridColumns, useGridFormSchema } from './data';

const [FormModal, formModalApi] = useVbenModal({
  // connectedComponent: Form,
  destroyOnClose: true,
});

function onRefresh() {
  gridApi.query();
}

function handleCreate() {
  formModalApi.setData({}).open();
}

function handleEdit(row: WhOrderInfoApi.WhOrderInfoVO) {
  formModalApi.setData(row).open();
}

async function handleDelete(row: WhOrderInfoApi.WhOrderInfoVO) {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting', [row.id]),
    duration: 0,
    key: 'action_process_msg',
  });
  try {
    await deleteWhOrderInfo(row.id as number);
    message.success($t('ui.actionMessage.deleteSuccess', [row.id]));
    onRefresh();
  } finally {
    hideLoading();
  }
}

async function handleDeleteBatch() {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting'),
    duration: 0,
    key: 'action_process_msg',
  });
  try {
    for (const id of checkedIds.value) {
      await deleteWhOrderInfo(id);
    }
    message.success($t('ui.actionMessage.deleteSuccess'));
    onRefresh();
  } finally {
    hideLoading();
  }
}

const checkedIds = ref<number[]>([]);
function handleRowCheckboxChange({
  records,
}: {
  records: WhOrderInfoApi.WhOrderInfoVO[];
}) {
  checkedIds.value = records.map((item) => item.id);
}

async function handleExport() {
  const data = await exportWhOrderInfo(await gridApi.formApi.getValues());
  downloadFileFromBlobPart({ fileName: '物流订单入仓.xls', source: data });
}

const toolbarActions: ActionItem[] = [
  {
    label: $t('ui.actionTitle.create', ['物流订单入仓']),
    type: 'primary',
    icon: ACTION_ICON.ADD,
    auth: ['logistics:whorderinfo:create'],
    onClick: handleCreate,
  },
  {
    label: $t('ui.actionTitle.export'),
    type: 'primary',
    icon: ACTION_ICON.DOWNLOAD,
    auth: ['logistics:whorderinfo:export'],
    onClick: handleExport,
  },
  {
    label: '批量删除',
    type: 'primary',
    danger: true,
    disabled: isEmpty(checkedIds),
    icon: ACTION_ICON.DELETE,
    auth: ['logistics:whorderinfo:delete'],
    onClick: handleDeleteBatch,
  },
];

function rowActions(row: WhOrderInfoApi.WhOrderInfoVO): ActionItem[] {
  return [
    {
      label: $t('common.edit'),
      type: 'link',
      icon: ACTION_ICON.EDIT,
      auth: ['logistics:whorderinfo:update'],
      onClick: handleEdit.bind(null, row),
    },
    {
      label: $t('common.delete'),
      type: 'link',
      danger: true,
      icon: ACTION_ICON.DELETE,
      auth: ['logistics:whorderinfo:delete'],
      popConfirm: {
        title: $t('ui.actionMessage.deleteConfirm', [row.orderNo]),
        confirm: handleDelete.bind(null, row),
      },
    },
  ];
}

// 1. 在 useVbenVxeGrid 外部定义 currentPage/pageSize 变量
let lastPageInfo = { currentPage: 1, pageSize: 20 };
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: useGridFormSchema(),
  },
  gridOptions: {
    columns: useGridColumns(),
    height: 'auto',
    pagerConfig: { enabled: true },
    seqConfig: {
      seqMethod({ rowIndex }) {
        const { currentPage, pageSize } = lastPageInfo;
        return (currentPage - 1) * pageSize + rowIndex + 1;
      },
    },
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          lastPageInfo = {
            currentPage: page.currentPage,
            pageSize: page.pageSize,
          };
          // 入库仓库为必输，未选时不请求
          if (!formValues.warehouse) return { list: [], total: 0 };
          // 处理创建时间区间
          const params = { ...formValues };
          if (
            Array.isArray(formValues.createTime) &&
            formValues.createTime.length === 2
          ) {
            params.startDate = dayjs(formValues.createTime[0]).format(
              'YYYY-MM-DD',
            );
            params.endDate = dayjs(formValues.createTime[1]).format(
              'YYYY-MM-DD',
            );
            delete params.createTime;
          }
          return await getWhOrderInfoPage({
            pageNo: page.currentPage,
            pageSize: page.pageSize,
            ...params,
          });
        },
      },
    },
    rowConfig: { keyField: 'id', isHover: true },
    toolbarConfig: { refresh: { code: 'query' }, search: true },
  } as VxeTableGridOptions<WhOrderInfoApi.WhOrderInfoVO>,
  gridEvents: {
    checkboxAll: handleRowCheckboxChange,
    checkboxChange: handleRowCheckboxChange,
  },
});
</script>
<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />
    <Grid table-title="物流订单入仓列表">
      <template #toolbar-tools>
        <TableAction :actions="toolbarActions" />
      </template>
      <template #actions="{ row }">
        <TableAction :actions="rowActions(row)" />
      </template>
    </Grid>
  </Page>
</template>
