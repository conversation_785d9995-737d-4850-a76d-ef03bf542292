import type { PageParam, PageResult } from '@vben/request';

import { requestClient } from '#/api/request';

export namespace ShippingBillInfoApi {
  export interface ShippingBillInfoVO {
    id: number;
    billOfLadingNumber: string;
    trackingNumber: string;
    estimatedDepartureChina: string; // DATE
    estimatedArrivalBeira: string; // DATE
    debitNoteChina: number;
    debitNoteClubfs: number;
    chinaCost: number;
    dutyTransportWarehouse: number;
    profit: number;
    refundAmountChina: number;
    createTime: string;
    creator: string;
    updater: string;
    updateTime: string;
    deleted: boolean;
  }
}

/** 查询提单信息分页 */
export function getShippingBillInfoPage(params: PageParam) {
  return requestClient.get<PageResult<ShippingBillInfoApi.ShippingBillInfoVO>>(
    '/logistics/shipping-bill/page',
    { params },
  );
}

/** 查询提单信息详情 */
export function getShippingBillInfo(id: number) {
  return requestClient.get<ShippingBillInfoApi.ShippingBillInfoVO>(
    `/logistics/shipping-bill/get?id=${id}`,
  );
}

/** 新增提单信息 */
export function createShippingBillInfo(
  data: ShippingBillInfoApi.ShippingBillInfoVO,
) {
  return requestClient.post('/logistics/shipping-bill/create', data);
}

/** 修改提单信息 */
export function updateShippingBillInfo(
  data: ShippingBillInfoApi.ShippingBillInfoVO,
) {
  return requestClient.put('/logistics/shipping-bill/update', data);
}

/** 删除提单信息 */
export function deleteShippingBillInfo(id: number) {
  return requestClient.delete(`/logistics/shipping-bill/delete?id=${id}`);
}

/** 导出提单信息 */
export function exportShippingBillInfo(params: any) {
  return requestClient.download(
    '/logistics/shipping-bill/export-excel',
    params,
  );
}
