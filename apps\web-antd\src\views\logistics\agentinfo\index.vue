<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { AgentInfoApi } from '#/api/logistics/agentinfo';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromBlobPart, isEmpty } from '@vben/utils';

import { message } from 'ant-design-vue';

import { ACTION_ICON, TableAction, useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteAgentInfo,
  exportAgentInfo,
  getAgentInfoPage,
} from '#/api/logistics/agentinfo';
import { $t } from '#/locales';

import { useGridColumns, useGridFormSchema } from './data';
import Form from './modules/form.vue';

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
});

/** 刷新表格 */
function onRefresh() {
  gridApi.query();
}

/** 创建代理信息 */
function handleCreate() {
  formModalApi.setData({}).open();
}

/** 编辑代理信息 */
function handleEdit(row: AgentInfoApi.AgentInfoVO) {
  formModalApi.setData(row).open();
}

/** 删除代理信息 */
async function handleDelete(row: AgentInfoApi.AgentInfoVO) {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting', [row.agentName]),
    duration: 0,
    key: 'action_process_msg',
  });
  try {
    await deleteAgentInfo(row.id as number);
    message.success($t('ui.actionMessage.deleteSuccess', [row.agentName]));
    onRefresh();
  } finally {
    hideLoading();
  }
}

/** 查看追踪页面 */
function handleViewTracking(row: AgentInfoApi.AgentInfoVO) {
  const url = `/cargo-tracking/${encodeURIComponent(row.agentName)}`;
  window.open(url, '_blank');
}

/** 批量删除代理信息 */
async function handleDeleteBatch() {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting'),
    duration: 0,
    key: 'action_process_msg',
  });
  try {
    // 逐个删除选中的记录
    for (const id of checkedIds.value) {
      await deleteAgentInfo(id);
    }
    message.success($t('ui.actionMessage.deleteSuccess'));
    onRefresh();
  } finally {
    hideLoading();
  }
}

const checkedIds = ref<number[]>([]);
function handleRowCheckboxChange({
  records,
}: {
  records: AgentInfoApi.AgentInfoVO[];
}) {
  checkedIds.value = records
    .map((item) => item.id)
    .filter((id): id is number => id !== undefined);
}

/** 导出表格 */
async function handleExport() {
  const data = await exportAgentInfo(await gridApi.formApi.getValues());
  downloadFileFromBlobPart({ fileName: '代理信息.xls', source: data });
}

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: useGridFormSchema(),
  },
  gridOptions: {
    columns: useGridColumns(),
    height: 'auto',
    pagerConfig: {
      enabled: true,
    },
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          return await getAgentInfoPage({
            pageNo: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
        },
      },
    },
    rowConfig: {
      keyField: 'id',
      isHover: true,
    },
    toolbarConfig: {
      refresh: { code: 'query' },
      search: true,
    },
  } as VxeTableGridOptions<AgentInfoApi.AgentInfoVO>,
  gridEvents: {
    checkboxAll: handleRowCheckboxChange,
    checkboxChange: handleRowCheckboxChange,
  },
});
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />

    <Grid table-title="代理信息列表">
      <template #toolbar-tools>
        <TableAction
          :actions="[
            {
              label: $t('ui.actionTitle.create', ['代理信息']),
              type: 'primary',
              icon: ACTION_ICON.ADD,
              onClick: handleCreate,
            },
            {
              label: $t('ui.actionTitle.export'),
              type: 'primary',
              icon: ACTION_ICON.DOWNLOAD,
              onClick: handleExport,
            },
            {
              label: '批量删除',
              type: 'primary',
              danger: true,
              disabled: isEmpty(checkedIds),
              icon: ACTION_ICON.DELETE,
              onClick: handleDeleteBatch,
            },
          ]"
        />
      </template>
      <template #agentLogo="{ row }">
        <a-image
          v-if="row.agentLogo"
          :width="60"
          :height="40"
          :src="row.agentLogo"
          :preview="true"
          style="
            object-fit: cover;
            border-radius: 4px;
            border: 1px solid #f0f0f0;
          "
        />
        <span v-else class="text-gray-400">暂无Logo</span>
      </template>
      <template #images="{ row }">
        <a-space>
          <a-image
            v-if="row.image1Url"
            :width="40"
            :height="30"
            :src="row.image1Url"
            :preview="true"
            style="object-fit: cover; border-radius: 4px"
          />
          <a-image
            v-if="row.image2Url"
            :width="40"
            :height="30"
            :src="row.image2Url"
            :preview="true"
            style="object-fit: cover; border-radius: 4px"
          />
        </a-space>
      </template>
      <template #queryImages="{ row }">
        <a-space>
          <a-image
            v-if="row.queryImage1Url"
            :width="30"
            :height="20"
            :src="row.queryImage1Url"
            :preview="true"
            style="object-fit: cover; border-radius: 4px"
          />
          <a-image
            v-if="row.queryImage2Url"
            :width="30"
            :height="20"
            :src="row.queryImage2Url"
            :preview="true"
            style="object-fit: cover; border-radius: 4px"
          />
          <a-image
            v-if="row.queryImage3Url"
            :width="30"
            :height="20"
            :src="row.queryImage3Url"
            :preview="true"
            style="object-fit: cover; border-radius: 4px"
          />
          <a-image
            v-if="row.queryImage4Url"
            :width="30"
            :height="20"
            :src="row.queryImage4Url"
            :preview="true"
            style="object-fit: cover; border-radius: 4px"
          />
        </a-space>
      </template>
      <template #actions="{ row }">
        <TableAction
          :actions="[
            {
              label: $t('common.edit'),
              type: 'link',
              icon: ACTION_ICON.EDIT,
              onClick: handleEdit.bind(null, row),
            },
            {
              label: '查看追踪页面',
              type: 'link',
              icon: 'i-carbon:view',
              onClick: () => handleViewTracking(row),
            },
            {
              label: $t('common.delete'),
              type: 'link',
              danger: true,
              icon: ACTION_ICON.DELETE,
              popConfirm: {
                title: $t('ui.actionMessage.deleteConfirm', [row.agentName]),
                confirm: handleDelete.bind(null, row),
              },
            },
          ]"
        />
      </template>
    </Grid>
  </Page>
</template>
