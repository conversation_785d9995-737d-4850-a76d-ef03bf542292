import { requestClient } from '#/api/request';

export namespace CargoTrackingApi {
  // 货物追踪信息
  export interface TrackingInfo {
    trackingNumber: string;
    shippingMark: string;
    orderNo?: string;
    ctn: number;
    volume: number;
    weight: number;
    debitNote: number;
    remark: string;
    agent: string;
    status: string;
    date: string;
    // eslint-disable-next-line no-use-before-define
    timeline?: TimelineItem[];
  }

  // 物流轨迹项
  export interface TimelineItem {
    time: string;
    location: string;
    description: string;
    status: string;
  }

  // 查询参数
  export interface TrackingQuery {
    type: 'orderNo' | 'shippingMark' | 'trackingNumber';
    value: string;
  }
}

/** 根据追踪号查询货物信息 */
export function getCargoByTrackingNumber(trackingNumber: string) {
  return requestClient.get<CargoTrackingApi.TrackingInfo>(
    '/public/cargo/tracking',
    { params: { trackingNumber } }
  );
}

/** 根据唛头查询货物信息 */
export function getCargoByShippingMark(shippingMark: string) {
  return requestClient.get<CargoTrackingApi.TrackingInfo>(
    '/public/cargo/shipping-mark',
    { params: { shippingMark } }
  );
}

/** 根据订单号查询货物信息 */
export function getCargoByOrderNo(orderNo: string) {
  return requestClient.get<CargoTrackingApi.TrackingInfo>(
    '/public/cargo/order',
    { params: { orderNo } }
  );
}

/** 通用货物查询接口 */
export function searchCargo(query: CargoTrackingApi.TrackingQuery) {
  return requestClient.get<CargoTrackingApi.TrackingInfo>(
    '/public/cargo/search',
    { params: query }
  );
}
