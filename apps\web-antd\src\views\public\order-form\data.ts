import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { OrderInfoApi } from '#/api/logistics/orderinfo';

import { z } from '#/adapter/form';

// 更新唛头和入库仓库的辅助函数
export function updateMarkNumberAndWarehouse() {
  if (typeof window !== 'undefined' && window.__vbenFormApi) {
    const formApi = window.__vbenFormApi;
    const currentValues = formApi.getValues();
    const customerTel = currentValues.customerTel || '';
    const shippingMethod = currentValues.shippingMethod;
    const agentName = window.__vbenAgentName || '';

    // 获取电话号码的后5位
    const digits = (customerTel || '').replaceAll(/\D/g, '');
    const baseMarkNumber = digits.slice(-5);

    // 根据运输方式拼接后缀
    let markNumber = baseMarkNumber;
    if (shippingMethod === 1) {
      markNumber = `${baseMarkNumber}-AIR`;
    } else if (shippingMethod === 2) {
      markNumber = `${baseMarkNumber}-SEA`;
    }

    // 设置唛头
    formApi.setFieldValue('markNumber', markNumber);
    if (window.__vbenMarkNumberQrRef) {
      window.__vbenMarkNumberQrRef.value = markNumber;
    }

    // 设置入库仓库
    let warehouse = '';
    if (agentName && shippingMethod === 1) {
      warehouse = `${agentName}-AIR`;
    } else if (agentName && shippingMethod === 2) {
      warehouse = `${agentName}-SEA`;
    } else if (agentName) {
      warehouse = agentName;
    }

    if (warehouse) {
      formApi.setFieldValue('warehouse', warehouse);
    }
  }
}

declare global {
  interface Window {
    __vbenFormApi: any;
    __vbenMarkNumberRef: any;
    __vbenMarkNumberQrRef: any;
    __vbenAgentName: string;
  }
}

/** 新增/修改的表单 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    { fieldName: 'id', component: 'Input', dependencies: { triggerFields: [''], show: () => false } },
    { fieldName: 'orderNo', component: 'Input', dependencies: { triggerFields: [''], show: () => false } },
    { fieldName: 'customerName', label: '客户名称', rules: 'required', component: 'Input', componentProps: { placeholder: '请输入客户名称' } },
    { fieldName: 'customerTel', label: '客户电话', rules: z.string().regex(/^[0-9+\s()]*$/, '仅允许输入数字、+、空格和括号').refine(val => (val.match(/\d/g)?.length ?? 0) > 5 && val.length > 0, '请输入电话且需包含至少6位数字'), component: 'Input', componentProps: {
      placeholder: '请输入客户电话',
      onChange: (e: any) => {
        console.warn('客户电话 onChange 触发:', e);
        const val = e?.target?.value ?? e;
        const digits = (val || '').replaceAll(/\D/g, '');
        const baseMarkNumber = digits.slice(-5);

        console.warn('客户电话处理:', { val, digits, baseMarkNumber });

        if (typeof window !== 'undefined' && window.__vbenFormApi) {
          const formApi = window.__vbenFormApi;

          // 获取当前运输方式
          setTimeout(async () => {
            try {
              const currentValues = await formApi.getValues();
              const shippingMethod = currentValues.shippingMethod;

              console.warn('客户电话联动 - 当前表单值:', { customerTel: val, shippingMethod });

              // 根据运输方式拼接后缀
              let markNumber = baseMarkNumber;
              if (shippingMethod === 1) {
                markNumber = `${baseMarkNumber}-AIR`;
              } else if (shippingMethod === 2) {
                markNumber = `${baseMarkNumber}-SEA`;
              }

              console.warn('客户电话联动 - 计算出的唛头:', markNumber);

              formApi.setFieldValue('markNumber', markNumber);
              if (window.__vbenMarkNumberQrRef) {
                window.__vbenMarkNumberQrRef.value = markNumber;
              }

              // 更新入库仓库
              const agentName = window.__vbenAgentName || '';
              if (agentName) {
                let warehouse = agentName;
                if (shippingMethod === 1) {
                  warehouse = `${agentName}-AIR`;
                } else if (shippingMethod === 2) {
                  warehouse = `${agentName}-SEA`;
                }

                console.warn('客户电话联动 - 计算出的入库仓库:', warehouse);
                formApi.setFieldValue('warehouse', warehouse);
              }
            } catch (error) {
              console.warn('客户电话联动失败，使用基础唛头:', error);
              // 如果获取表单值失败，只设置基础唛头
              formApi.setFieldValue('markNumber', baseMarkNumber);
              if (window.__vbenMarkNumberQrRef) {
                window.__vbenMarkNumberQrRef.value = baseMarkNumber;
              }
            }
          }, 100);
        }
      }
    } },
    { fieldName: 'supplierName', label: '供应商名称', rules: 'required', component: 'Input', componentProps: { placeholder: '请输入供应商名称' } },
    { fieldName: 'supplierTel', label: '供应商电话', rules: z.string().regex(/^[0-9+\s()]*$/, '仅允许输入数字、+、空格和括号').refine(val => (val.match(/\d/g)?.length ?? 0) > 5 && val.length > 0, '请输入电话且需包含至少6位数字'), component: 'Input', componentProps: { placeholder: '请输入供应商电话' } },
    { fieldName: 'shippingMethod', label: '运输方式', rules: 'required', component: 'Select', componentProps: {
      placeholder: '请选择运输方式',
      options: [ { label: '空运', value: 1 }, { label: '海运', value: 2 }],
      onChange: (value: any) => {
        console.warn('运输方式选择:', value);
        if (typeof window !== 'undefined' && window.__vbenFormApi) {
          const formApi = window.__vbenFormApi;

          setTimeout(async () => {
            try {
              const currentValues = await formApi.getValues();
              const customerTel = currentValues.customerTel || '';
              const digits = (customerTel || '').replaceAll(/\D/g, '');
              const baseMarkNumber = digits.slice(-5);

              console.warn('当前表单值:', { customerTel, baseMarkNumber, shippingMethod: value });

              // 根据运输方式拼接后缀
              let markNumber = baseMarkNumber;
              if (value === 1) {
                markNumber = `${baseMarkNumber}-AIR`;
              } else if (value === 2) {
                markNumber = `${baseMarkNumber}-SEA`;
              }

              console.warn('计算出的唛头:', markNumber);

              formApi.setFieldValue('markNumber', markNumber);
              if (window.__vbenMarkNumberQrRef) {
                window.__vbenMarkNumberQrRef.value = markNumber;
              }

              // 更新入库仓库
              const agentName = window.__vbenAgentName || '';
              if (agentName) {
                let warehouse = agentName;
                if (value === 1) {
                  warehouse = `${agentName}-AIR`;
                } else if (value === 2) {
                  warehouse = `${agentName}-SEA`;
                }

                console.warn('计算出的入库仓库:', warehouse);
                formApi.setFieldValue('warehouse', warehouse);
              }
            } catch (error) {
              console.error('更新运输方式联动失败:', error);
            }
          }, 100);
        }
      }
    } },
    { fieldName: 'markNumber', label: '唛头', component: 'Input', componentProps: { readonly: true, placeholder: '自动截取客户电话后5位' } },
    { fieldName: 'markNumberQr', label: '唛头条形码', component: 'Render' },
    { fieldName: 'trackingNumber', label: '物流条码', component: 'Input', componentProps: { placeholder: '请输入物流条码' } },
    { fieldName: 'warehouse', label: '入库仓库', rules: 'required', component: 'Input', componentProps: { readonly: true, placeholder: '根据代理和运输方式自动生成' } },
    { fieldName: 'quantity', label: '包装件数', rules: z.number().gt(0, '包装件数必须大于0'), component: 'InputNumber', componentProps: { placeholder: '请输入包装件数' } },
    { fieldName: 'volume', label: '体积(CBM) m³', rules: z.number().gt(0, '体积必须大于0').refine(val => Number(val.toFixed(3)) === val, '最多3位小数'), component: 'InputNumber', componentProps: { placeholder: '请输入体积(CBM)', step: 0.001, precision: 3 } },
    { fieldName: 'weight', label: '重量(kg)', rules: z.number().gt(0, '重量必须大于0').refine(val => Number(val.toFixed(3)) === val, '最多3位小数'), component: 'InputNumber', componentProps: { placeholder: '请输入重量', step: 0.001, precision: 3 } },
    { fieldName: 'imgUrl', label: '图片', component: 'ImageUpload', componentProps: { maxNumber: 1, fileSize: 5, fileType: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'], width: '120px', height: '80px' }, dependencies: { triggerFields: [''], show: () => false } },
  ];
}

/** 列表的搜索表单 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    { fieldName: 'orderNo', label: '单号', component: 'Input', componentProps: { allowClear: true, placeholder: '请输入单号' } },
    { fieldName: 'supplierName', label: '供应商名称', component: 'Input', componentProps: { allowClear: true, placeholder: '请输入供应商名称' } },
    { fieldName: 'supplierTel', label: '供应商电话', component: 'Input', rules: z.string().regex(/^[0-9+\s()]*$/, '仅允许输入数字、+、空格和括号').optional(), componentProps: { allowClear: true, placeholder: '请输入供应商电话' } },
    { fieldName: 'customerName', label: '客户名称', component: 'Input', componentProps: { allowClear: true, placeholder: '请输入客户名称' } },
    { fieldName: 'customerTel', label: '客户电话', component: 'Input', rules: z.string().regex(/^[0-9+\s()]*$/, '仅允许输入数字、+、空格和括号').optional(), componentProps: { allowClear: true, placeholder: '请输入客户电话' } },
    { fieldName: 'markNumber', label: '唛头', component: 'Input', componentProps: { allowClear: true, placeholder: '请输入唛头' } },
    { fieldName: 'trackingNumber', label: '物流条码', component: 'Input', componentProps: { allowClear: true, placeholder: '请输入物流条码' } },
    { fieldName: 'warehouse', label: '入库仓库', component: 'Input', componentProps: { allowClear: true, placeholder: '请输入入库仓库' } },
    { fieldName: 'shippingMethod', label: '运输方式', component: 'Select', componentProps: { allowClear: true, placeholder: '请选择运输方式', options: [ { label: '空运', value: 1 }, { label: '海运', value: 2 }, { label: '陆运', value: 3 } ] } },
    { fieldName: 'imgUrl', label: '图片', component: 'Input', componentProps: { allowClear: true, placeholder: '请输入图片URL' } },
    { fieldName: 'createTime', label: '创建时间', component: 'RangePicker', componentProps: { allowClear: true } },
  ];
}

/** 列表的字段 */
export function useGridColumns(): VxeTableGridOptions<OrderInfoApi.OrderInfoVO>['columns'] {
  return [
    { type: 'checkbox', width: 40 },
    { field: 'id', title: '主键', minWidth: 80 },
    { field: 'orderNo', title: '单号', minWidth: 120 },
    { field: 'supplierName', title: '供应商名称', minWidth: 120 },
    { field: 'supplierTel', title: '供应商电话', minWidth: 120 },
    { field: 'customerName', title: '客户名称', minWidth: 120 },
    { field: 'customerTel', title: '客户电话', minWidth: 120 },
    { field: 'markNumber', title: '唛头', minWidth: 120 },
    { field: 'trackingNumber', title: '物流条码', minWidth: 120 },
    { field: 'warehouse', title: '入库仓库', minWidth: 120 },
    { field: 'shippingMethod', title: '运输方式', minWidth: 100 },
    { field: 'quantity', title: '包装件数', minWidth: 100 },
    { field: 'volume', title: '体积(CBM)', minWidth: 100 },
    { field: 'weight', title: '重量', minWidth: 100 },
    { field: 'imgUrl', title: '图片', minWidth: 100, cellRender: { name: 'CellImage' } },
    { field: 'createTime', title: '创建时间', minWidth: 120, formatter: 'formatDateTime' },
    { field: 'updateTime', title: '更新时间', minWidth: 120, formatter: 'formatDateTime' },
    { field: 'creator', title: '创建者', minWidth: 100 },
    { field: 'updater', title: '更新者', minWidth: 100 },
    { title: '操作', width: 160, fixed: 'right', slots: { default: 'actions' } },
  ];
}
