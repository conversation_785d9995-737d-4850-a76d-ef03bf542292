import type { PageParam, PageResult } from '@vben/request';

import { requestClient } from '#/api/request';

export namespace ShippingBillOrderRelationApi {
  export interface ShippingBillOrderRelationVO {
    id: number;
    billOfLadingNumber: string;
    orderNo: string;
    outstandingCharges: number;
    createTime: string;
    creator: string;
    updater: string;
    updateTime: string;
    deleted: boolean;
  }
}

/** 查询提单订单关系分页 */
export function getShippingBillOrderRelationPage(params: PageParam) {
  return requestClient.get<
    PageResult<ShippingBillOrderRelationApi.ShippingBillOrderRelationVO>
  >('/logistics/shipping-bill-order-relation/page', { params });
}

/** 查询提单订单关系详情 */
export function getShippingBillOrderRelation(id: number) {
  return requestClient.get<ShippingBillOrderRelationApi.ShippingBillOrderRelationVO>(
    `/logistics/shipping-bill-order-relation/get?id=${id}`,
  );
}

/** 批量绑定订单到提单 */
export function batchBindOrdersToBill(
  billOfLadingNumber: string,
  orderNos: string[],
  outstandingCharges?: number[],
) {
  const data: Record<string, any> = {
    billOfLadingNumber,
    orderNos,
  };

  if (outstandingCharges && outstandingCharges.length > 0) {
    data.outstandingCharges = outstandingCharges;
  }

  return requestClient.post('/logistics/shipping-bill/batch-bind-orders', data);
}

/** 解绑订单 */
export function unbindOrderFromBill(
  billOfLadingNumber: string,
  orderNo: string,
) {
  return requestClient.delete('/logistics/shipping-bill/unbind-order', {
    params: { billOfLadingNumber, orderNo },
  });
}

/** 获取提单关联的订单列表 */
export function getBillOrderRelations(billOfLadingNumber: string) {
  return requestClient.get<
    ShippingBillOrderRelationApi.ShippingBillOrderRelationVO[]
  >('/logistics/shipping-bill/orders', { params: { billOfLadingNumber } });
}
