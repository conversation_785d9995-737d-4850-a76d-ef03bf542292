import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { ShippingBillInfoApi } from '#/api/logistics/shippingbillinfo';

/** 列表的搜索表单 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    { fieldName: 'billOfLadingNumber', label: '提单编号', component: 'Input', componentProps: { allowClear: true, placeholder: '请输入提单编号' } },
    { fieldName: 'trackingNumber', label: '国际物流编号', component: 'Input', componentProps: { allowClear: true, placeholder: '请输入国际物流编号' } },
    { fieldName: 'estimatedDepartureChina', label: '预计从中国出发时间', component: 'DatePicker', componentProps: { allowClear: true } },
    { fieldName: 'estimatedArrivalBeira', label: '预计到达贝拉时间', component: 'DatePicker', componentProps: { allowClear: true } },
    { fieldName: 'createTime', label: '创建时间', component: 'RangePicker', componentProps: { allowClear: true } },
  ];
}

/** 列表的字段 */
export function useGridColumns(): VxeTableGridOptions<ShippingBillInfoApi.ShippingBillInfoVO>['columns'] {
  return [
    { type: 'checkbox', width: 40 },
    { type: 'seq', title: '序号', width: 60 },
    { field: 'billOfLadingNumber', title: '提单编号', minWidth: 160 },
    { field: 'trackingNumber', title: '国际物流编号', minWidth: 160 },
    { field: 'estimatedDepartureChina', title: '预计从中国出发时间', minWidth: 140 },
    { field: 'estimatedArrivalBeira', title: '预计到达贝拉时间', minWidth: 140 },
    { field: 'debitNoteChina', title: '中国方借记通知单(USD)', minWidth: 140 },
    { field: 'debitNoteClubfs', title: 'Clubfs借记通知单(USD)', minWidth: 140 },
    { field: 'chinaCost', title: '中国境内费用', minWidth: 120 },
    { field: 'dutyTransportWarehouse', title: '关税+运输+仓储', minWidth: 140 },
    { field: 'profit', title: '利润', minWidth: 100 },
    { field: 'refundAmountChina', title: '需退还中国方金额', minWidth: 140 },
    { field: 'createTime', title: '创建时间', minWidth: 120, formatter: 'formatDateTime' },
    { field: 'updateTime', title: '更新时间', minWidth: 120, formatter: 'formatDateTime' },
    { field: 'creator', title: '创建者', minWidth: 100 },
    { field: 'updater', title: '更新者', minWidth: 100 },
    { title: '操作', width: 160, fixed: 'right', slots: { default: 'actions' } },
  ];
} 
