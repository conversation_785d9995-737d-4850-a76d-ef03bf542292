<script lang="ts" setup>
import type { CargoTrackingApi } from '#/api/public/cargo-tracking';
import type { Express100Api } from '#/api/public/express100';

import { onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import { message } from 'ant-design-vue';

import { getAgentQueryImages } from '#/api/logistics/agentinfo';

// 路由相关
const route = useRoute();

// 代理相关
const agentName = ref<string>('');

// 搜索相关
const searchType = ref<'orderNo' | 'shippingMark' | 'trackingNumber'>('trackingNumber');
const searchValue = ref('');
const searching = ref(false);
const trackingResult = ref<CargoTrackingApi.TrackingInfo | null>(null);
const noResult = ref(false);

// 快递追踪相关
const expressTracking = ref<Express100Api.TrackingResponse | null>(null);
const expressLoading = ref(false);

// 广告图片
const adImages = ref<string[]>([
  '/src/views/logistics/orderinfo/65e19528ba249.jpg',
  '/src/views/logistics/orderinfo/65e19528ba249.jpg',
  '/src/views/logistics/orderinfo/65e19528ba249.jpg',
  '/src/views/logistics/orderinfo/65e19528ba249.jpg',
]);

// 搜索类型选项
const searchTypeOptions = [
  { label: 'Tracking No.', value: 'trackingNumber' },
  { label: 'Shipping Mark', value: 'shippingMark' },
  { label: 'Order No.', value: 'orderNo' },
];

// 结果表格列定义
const resultColumns = [
  {
    title: 'Tracking number',
    dataIndex: 'trackingNumber',
    key: 'trackingNumber',
    width: 150,
  },
  {
    title: 'Shipping mark',
    dataIndex: 'shippingMark',
    key: 'shippingMark',
    width: 120,
  },
  {
    title: 'CTN',
    dataIndex: 'ctn',
    key: 'ctn',
    width: 80,
  },
  {
    title: 'CBM',
    dataIndex: 'volume',
    key: 'volume',
    width: 80,
  },
  {
    title: 'Weight',
    dataIndex: 'weight',
    key: 'weight',
    width: 100,
  },
  {
    title: 'Debit note(USD)',
    dataIndex: 'debitNote',
    key: 'debitNote',
    width: 120,
  },
  {
    title: 'Remark',
    dataIndex: 'remark',
    key: 'remark',
    width: 120,
  },
  {
    title: 'Agent',
    dataIndex: 'agent',
    key: 'agent',
    width: 100,
  },
  {
    title: 'Status',
    dataIndex: 'status',
    key: 'status',
    width: 120,
  },
  {
    title: 'Date',
    dataIndex: 'date',
    key: 'date',
    width: 100,
  },
];

// 获取输入框占位符
function getPlaceholder() {
  const placeholders = {
    trackingNumber: '请输入追踪号码',
    shippingMark: '请输入唛头',
    orderNo: '请输入订单号',
  };
  return placeholders[searchType.value] || '请输入查询内容';
}

// 获取状态颜色
function getStatusColor(status: string) {
  const colors: Record<string, string> = {
    'RECEIVED_AT_WAREHOUSE': 'blue',
    'IN_TRANSIT': 'orange',
    'DELIVERED': 'green',
    'PENDING': 'default',
  };
  return colors[status] || 'default';
}

// 获取状态文本
function getStatusText(status: string) {
  const texts: Record<string, string> = {
    'RECEIVED_AT_WAREHOUSE': '已入仓',
    'IN_TRANSIT': '运输中',
    'DELIVERED': '已送达',
    'PENDING': '待处理',
  };
  return texts[status] || status;
}

// 获取时间轴颜色
function getTimelineColor(status: string) {
  const colors: Record<string, string> = {
    'RECEIVED_AT_WAREHOUSE': 'blue',
    'IN_TRANSIT': 'orange',
    'DELIVERED': 'green',
    'PENDING': 'gray',
  };
  return colors[status] || 'gray';
}

// 执行搜索
async function handleSearch() {
  if (!searchValue.value.trim()) {
    message.warning('请输入查询内容');
    return;
  }

  searching.value = true;
  noResult.value = false;
  trackingResult.value = null;

  try {
    // 暂时使用模拟数据，后续替换为真实API
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 模拟查询结果
    const mockResult: CargoTrackingApi.TrackingInfo = {
      trackingNumber: searchValue.value.trim(),
      shippingMark: '22322',
      ctn: 1,
      volume: 0.04,
      weight: 4.2,
      debitNote: 0,
      remark: '117023',
      agent: 'ChuFS Sea',
      status: 'RECEIVED_AT_WAREHOUSE',
      date: '2025-06-02',
      timeline: [
        {
          time: '2025-06-02 14:30',
          location: '广州仓库',
          description: 'RECEIVED AT WAREHOUSE',
          status: 'RECEIVED_AT_WAREHOUSE',
        },
        {
          time: '2025-06-01 09:15',
          location: '深圳集货点',
          description: '货物已收取，准备入仓',
          status: 'PENDING',
        },
      ],
    };

    trackingResult.value = mockResult;

    // 如果查询到物流码，同时查询快递100轨迹
    if (mockResult.trackingNumber) {
      await fetchExpressTracking(mockResult.trackingNumber);
    }

    // 根据代理加载查询图片
    if (mockResult.agent) {
      await loadAgentQueryImages(mockResult.agent);
    }

    // 真实API调用（注释掉，等后端接口准备好后启用）
    // const result = await searchCargo({
    //   type: searchType.value,
    //   value: searchValue.value.trim(),
    // });
    // if (result) {
    //   trackingResult.value = result;
    //   // 查询快递轨迹
    //   if (result.trackingNumber) {
    //     await fetchExpressTracking(result.trackingNumber);
    //   }
    // } else {
    //   noResult.value = true;
    // }
  } catch (error) {
    console.error('查询失败:', error);
    noResult.value = true;
    message.error('未找到相关货物信息，请检查查询内容是否正确');
  } finally {
    searching.value = false;
  }
}

// 查询快递100轨迹
async function fetchExpressTracking(trackingNumber: string) {
  if (!trackingNumber) return;

  expressLoading.value = true;
  try {
    // 暂时使用模拟数据，后续替换为真实快递100 API
    await new Promise(resolve => setTimeout(resolve, 800));

    // 模拟快递100数据
    const mockExpressData: Express100Api.TrackingResponse = {
      message: 'ok',
      nu: trackingNumber,
      ischeck: '1',
      condition: 'F00',
      com: 'yuantong',
      status: '200',
      state: '3',
      data: [
        {
          time: '2025-01-26 14:30:00',
          ftime: '2025-01-26 14:30',
          context: '快件已签收，签收人：本人签收',
          location: '广州市天河区',
        },
        {
          time: '2025-01-26 09:15:00',
          ftime: '2025-01-26 09:15',
          context: '快件正在派送中，派送员：张师傅',
          location: '广州市天河区营业部',
        },
        {
          time: '2025-01-25 18:20:00',
          ftime: '2025-01-25 18:20',
          context: '快件到达广州市天河区营业部',
          location: '广州市天河区营业部',
        },
        {
          time: '2025-01-25 12:30:00',
          ftime: '2025-01-25 12:30',
          context: '快件离开深圳转运中心，发往广州',
          location: '深圳转运中心',
        },
      ],
    };

    expressTracking.value = mockExpressData;

    // 真实API调用（注释掉，等快递100接口准备好后启用）
    // const companyResult = await autoDetectExpressCompany(trackingNumber);
    // if (companyResult && companyResult.code) {
    //   const trackingData = await getExpressTracking({
    //     com: companyResult.code,
    //     num: trackingNumber,
    //   });
    //   if (trackingData && trackingData.data) {
    //     expressTracking.value = trackingData;
    //   }
    // }

    if (mockExpressData && mockExpressData.data && // 将快递100的轨迹数据合并到现有的timeline中
      trackingResult.value) {
        const express100Timeline = mockExpressData.data.map((item: Express100Api.TrackingItem) => ({
          time: item.ftime || item.time,
          location: item.location || '快递网点',
          description: item.context,
          status: getExpressStatus(mockExpressData.state),
        }));

        // 合并轨迹数据
        trackingResult.value.timeline = [
          ...express100Timeline,
          ...(trackingResult.value.timeline || [])
        ].sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime());
      }
  } catch (error) {
    console.warn('快递100查询失败:', error);
    // 快递查询失败不影响主要功能，只记录警告
  } finally {
    expressLoading.value = false;
  }
}

// 将快递100状态转换为系统状态
function getExpressStatus(state: string): string {
  const stateMap: Record<string, string> = {
    '0': 'PENDING',        // 在途
    '1': 'PENDING',        // 揽收
    '2': 'IN_TRANSIT',     // 疑难
    '3': 'DELIVERED',      // 签收
    '4': 'PENDING',        // 退签
    '5': 'IN_TRANSIT',     // 派件
    '6': 'PENDING',        // 退回
  };
  return stateMap[state] || 'PENDING';
}

// 加载代理查询图片
async function loadAgentQueryImages(agentName: string) {
  try {
    const agentInfo = await getAgentQueryImages(agentName);
    if (agentInfo) {
      // 更新查询图片（按位置更新）
      const queryImages = [
        agentInfo.queryImage1Url,
        agentInfo.queryImage2Url,
        agentInfo.queryImage3Url,
        agentInfo.queryImage4Url,
      ].filter(Boolean); // 过滤掉空值

      if (queryImages.length > 0) {
        // 按位置更新图片，保持4个位置
        const newAdImages = [...adImages.value];
        queryImages.forEach((imageUrl, index) => {
          if (index < 4 && imageUrl) {
            newAdImages[index] = imageUrl;
          }
        });
        adImages.value = newAdImages;
      }
    }
  } catch (error) {
    console.warn('加载代理查询图片失败:', error);
    // 使用默认图片
  }
}

// 初始化代理信息
async function initializeAgentInfo() {
  const routeAgentName = route.params.agentName as string;
  if (routeAgentName) {
    agentName.value = routeAgentName;
    await loadAgentQueryImages(routeAgentName);
  }
}

// 监听路由参数变化
watch(() => route.params.agentName, async (newAgentName) => {
  if (newAgentName && typeof newAgentName === 'string') {
    agentName.value = newAgentName;
    await loadAgentQueryImages(newAgentName);
  }
}, { immediate: true });

// 组件挂载时初始化
onMounted(() => {
  initializeAgentInfo();
});
</script>

<template>
  <div class="cargo-tracking-page">
    <!-- 背景容器 -->
    <div class="bg-container">
      <!-- 主要内容区域 -->
      <div class="content-wrapper">
        <!-- 标题区域 -->
        <div class="header-section">
          <h1 class="main-title">Cargo tracking system</h1>
          <p class="subtitle">货物查询 Track your shipments</p>
        </div>

        <!-- 查询表单 -->
        <div class="search-section">
          <a-card class="search-card" :bordered="false">
            <div class="search-form">
              <div class="input-group">
                <a-select
                  v-model:value="searchType"
                  class="search-type-select"
                  :options="searchTypeOptions"
                />
                <a-input
                  v-model:value="searchValue"
                  class="search-input"
                  :placeholder="getPlaceholder()"
                  @press-enter="handleSearch"
                />
                <a-button
                  type="primary"
                  class="search-button"
                  :loading="searching"
                  @click="handleSearch"
                >
                  开始追踪TRACK
                </a-button>
              </div>
            </div>
          </a-card>
        </div>

        <!-- 查询结果 -->
        <div v-if="trackingResult" class="result-section">
          <a-card class="result-card" :bordered="false">
            <a-table
              :columns="resultColumns"
              :data-source="[trackingResult]"
              :pagination="false"
              size="middle"
              :scroll="{ x: 800 }"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'status'">
                  <a-tag :color="getStatusColor(record.status)">
                    {{ getStatusText(record.status) }}
                  </a-tag>
                </template>
                <template v-if="column.key === 'weight'">
                  {{ record.weight }} KG
                </template>
                <template v-if="column.key === 'volume'">
                  {{ record.volume }} CBM
                </template>
              </template>
            </a-table>

            <!-- 物流轨迹 -->
            <div v-if="trackingResult.timeline" class="timeline-section">
              <div class="timeline-header">
                <h3 class="timeline-title">物流轨迹</h3>
                <a-spin v-if="expressLoading" size="small" />
                <a-tag v-if="expressTracking" color="blue" class="express-tag">
                  快递100数据
                </a-tag>
              </div>
              <a-timeline>
                <a-timeline-item
                  v-for="(item, index) in trackingResult.timeline"
                  :key="index"
                  :color="getTimelineColor(item.status)"
                >
                  <div class="timeline-content">
                    <div class="timeline-time">{{ item.time }}</div>
                    <div class="timeline-location">{{ item.location }}</div>
                    <div class="timeline-description">{{ item.description }}</div>
                  </div>
                </a-timeline-item>
              </a-timeline>
            </div>
          </a-card>
        </div>

        <!-- 无结果提示 -->
        <div v-if="noResult" class="no-result">
          <a-empty description="未找到相关货物信息，请检查追踪号码是否正确" />
        </div>

        <!-- 广告图片区域 -->
        <div class="ads-section">
          <div class="ads-container">
            <div class="ad-item" v-for="(image, index) in adImages" :key="index">
              <img
                :src="image"
                :alt="`广告 ${index + 1}`"
                class="ad-image"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 简单的页脚 -->
    <footer class="public-footer">
      <div class="footer-content">
        <p>&copy; 2025 物流管理系统. All rights reserved.</p>
      </div>
    </footer>
  </div>
</template>

<style lang="scss" scoped>
.cargo-tracking-page {
  height: 100vh;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.public-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;

  .header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    .logo-section {
      .logo-image {
        height: 60px;
        width: auto;
        object-fit: contain;
      }
    }
  }
}

.bg-container {
  flex: 1;
  background: url('/bj1.jpg') no-repeat center center;
  background-size: cover;
  background-attachment: fixed;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 0;
  }
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  position: relative;
  z-index: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.header-section {
  text-align: center;
  margin-bottom: 30px;

  .main-title {
    font-size: 36px;
    font-weight: 300;
    color: white;
    margin-bottom: 12px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .subtitle {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
  }
}

.search-section {
  margin-bottom: 20px;
  
  .search-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }
  
  .search-form {
    padding: 15px;
    
    .input-group {
      display: flex;
      gap: 12px;
      align-items: center;
      
      .search-type-select {
        width: 150px;
        flex-shrink: 0;
      }
      
      .search-input {
        flex: 1;
        height: 48px;
        font-size: 16px;
      }
      
      .search-button {
        height: 48px;
        padding: 0 32px;
        font-size: 16px;
        font-weight: 500;
        border-radius: 6px;
        flex-shrink: 0;
      }
    }
  }
}

.result-section {
  .result-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }
}

.timeline-section {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;

  .timeline-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 24px;

    .timeline-title {
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
      margin: 0;
    }

    .express-tag {
      font-size: 12px;
    }
  }
  
  .timeline-content {
    .timeline-time {
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 4px;
    }
    
    .timeline-location {
      color: #6b7280;
      margin-bottom: 2px;
    }
    
    .timeline-description {
      color: #9ca3af;
      font-size: 14px;
    }
  }
}

.no-result {
  text-align: center;
  padding: 60px 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.ads-section {
  margin-top: 20px;

  .ads-container {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: nowrap;

    .ad-item {
      flex: 1;
      max-width: 250px;

      .ad-image {
        width: 100%;
        height: auto;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }
}

.public-footer {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);

  .footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 10px 20px;
    text-align: center;

    p {
      margin: 0;
      color: #6b7280;
      font-size: 14px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .header-section {
    .main-title {
      font-size: 32px;
    }
    
    .subtitle {
      font-size: 16px;
    }
  }
  
  .search-form {
    .input-group {
      flex-direction: column;
      
      .search-type-select,
      .search-input,
      .search-button {
        width: 100%;
      }
    }
  }
  
  .content-wrapper {
    padding: 20px 16px;
  }
}
</style>
