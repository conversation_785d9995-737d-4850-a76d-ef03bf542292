import { requestClient } from '#/api/request';

export namespace Express100Api {
  // 快递100查询参数
  export interface TrackingQuery {
    com: string; // 快递公司编码
    num: string; // 快递单号
    phone?: string; // 手机号后四位（顺丰必填）
  }

  // 物流轨迹项
  export interface TrackingItem {
    time: string; // 时间
    ftime: string; // 格式化时间
    context: string; // 描述
    location?: string; // 地点
    status?: string; // 状态
  }

  // 快递100响应数据
  export interface TrackingResponse {
    message: string; // 消息
    nu: string; // 单号
    ischeck: string; // 是否签收标记
    condition: string; // 快递单当前状态
    com: string; // 快递公司编码
    status: string; // 查询结果状态
    state: string; // 快递单明细状态
    data: TrackingItem[]; // 物流轨迹
  }

  // 快递公司信息
  export interface ExpressCompany {
    code: string; // 公司编码
    name: string; // 公司名称
  }
}

// 常用快递公司列表
export const EXPRESS_COMPANIES: Express100Api.ExpressCompany[] = [
  { code: 'shentong', name: '申通快递' },
  { code: 'yuantong', name: '圆通速递' },
  { code: 'zhongtong', name: '中通快递' },
  { code: 'yunda', name: '韵达速递' },
  { code: 'shunfeng', name: '顺丰速运' },
  { code: 'ems', name: 'EMS' },
  { code: 'jingdong', name: '京东快递' },
  { code: 'debangwuliu', name: '德邦快递' },
  { code: 'tiantian', name: '天天快递' },
  { code: 'huitongkuaidi', name: '百世快递' },
];

/** 查询快递物流轨迹 */
export function getExpressTracking(query: Express100Api.TrackingQuery) {
  return requestClient.get<Express100Api.TrackingResponse>(
    '/public/express/tracking',
    { params: query }
  );
}

/** 自动识别快递公司 */
export function autoDetectExpressCompany(trackingNumber: string) {
  return requestClient.get<{ code: string; name: string }>(
    '/public/express/auto-detect',
    { params: { num: trackingNumber } }
  );
}

/** 获取所有支持的快递公司列表 */
export function getExpressCompanies() {
  return requestClient.get<Express100Api.ExpressCompany[]>(
    '/public/express/companies'
  );
}
