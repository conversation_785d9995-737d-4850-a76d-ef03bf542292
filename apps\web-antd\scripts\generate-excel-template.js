import ExcelJS from 'exceljs';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * 生成静态Excel模板文件
 */
async function generateDeliveryNoteTemplate() {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Delivery Note');

  // 设置页面设置
  worksheet.pageSetup = {
    paperSize: 9, // A4
    orientation: 'portrait',
    margins: {
      left: 0.5,
      right: 0.5,
      top: 0.5,
      bottom: 0.5,
      header: 0,
      footer: 0,
    },
  };

  // 设置列宽
  worksheet.columns = [
    { width: 8 },   // A
    { width: 12 },  // B
    { width: 15 },  // C
    { width: 12 },  // D
    { width: 15 },  // E
    { width: 12 },  // F
    { width: 15 },  // G
    { width: 10 },  // H
  ];

  // 添加T&C Logo区域
  const logoRow = worksheet.getRow(1);
  logoRow.height = 80;
  worksheet.getRow(2).height = 30;
  worksheet.getRow(3).height = 30;
  
  // 合并logo单元格
  worksheet.mergeCells('A1:C3');
  const logoCell = worksheet.getCell('A1');
  logoCell.value = 'T&C\nTOUCHTRANS &\nCLUB FREIGHT SERVICES';
  logoCell.font = { name: 'Arial', size: 12, bold: true, color: { argb: 'FF0066CC' } };
  logoCell.alignment = { vertical: 'middle', horizontal: 'center', wrapText: true };
  logoCell.fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFF0F8FF' }
  };
  logoCell.border = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    bottom: { style: 'thin' },
    right: { style: 'thin' },
  };

  // 添加主标题 "Delivery Note"
  worksheet.mergeCells('D1:F2');
  const titleCell = worksheet.getCell('D1');
  titleCell.value = 'Delivery Note';
  titleCell.font = { name: 'Arial', size: 24, bold: true };
  titleCell.alignment = { vertical: 'middle', horizontal: 'center' };

  // 添加序列号占位符
  const serialCell = worksheet.getCell('G1');
  serialCell.value = '{{SERIAL_NUMBER}}';
  serialCell.font = { name: 'Arial', size: 16, bold: true };
  serialCell.alignment = { vertical: 'middle', horizontal: 'right' };

  // 添加红色提示文字
  worksheet.mergeCells('A4:G4');
  const noticeCell = worksheet.getCell('A4');
  noticeCell.value = '送货前请先联系 Sean 18188802770( 微信）填写此单贴在外包装上';
  noticeCell.font = { name: 'Arial', size: 11, bold: true, color: { argb: 'FFE53935' } };
  noticeCell.alignment = { vertical: 'middle', horizontal: 'center' };

  // 创建主要信息表格
  const startRow = 6;
  
  // 表格边框样式
  const borderStyle = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    bottom: { style: 'thin' },
    right: { style: 'thin' },
  };

  // 第一行：唛头信息
  const row1 = worksheet.getRow(startRow);
  row1.height = 40;
  
  // 唛头 Mark
  const markLabelCell = worksheet.getCell(`A${startRow}`);
  markLabelCell.value = '唛头 Mark';
  markLabelCell.font = { name: 'Arial', size: 10, bold: true };
  markLabelCell.alignment = { vertical: 'middle', horizontal: 'center' };
  markLabelCell.border = borderStyle;

  // 唛头值占位符
  worksheet.mergeCells(`B${startRow}:C${startRow}`);
  const markValueCell = worksheet.getCell(`B${startRow}`);
  markValueCell.value = '{{MARK_NUMBER}}';
  markValueCell.font = { name: 'Arial', size: 14, bold: true };
  markValueCell.alignment = { vertical: 'middle', horizontal: 'center' };
  markValueCell.border = borderStyle;

  // 运输方式标识占位符
  const shippingCell = worksheet.getCell(`D${startRow}`);
  shippingCell.value = '{{SHIPPING_METHOD}}';
  shippingCell.font = { name: 'Arial', size: 9 };
  shippingCell.alignment = { vertical: 'middle', horizontal: 'center', wrapText: true };
  shippingCell.border = borderStyle;

  // 唛头条码
  const barcodeCell = worksheet.getCell(`E${startRow}`);
  barcodeCell.value = '唛头条码';
  barcodeCell.font = { name: 'Arial', size: 10 };
  barcodeCell.alignment = { vertical: 'middle', horizontal: 'center' };
  barcodeCell.border = borderStyle;

  // 条形码区域占位符
  worksheet.mergeCells(`F${startRow}:G${startRow}`);
  const barcodeAreaCell = worksheet.getCell(`F${startRow}`);
  barcodeAreaCell.value = '{{BARCODE}}';
  barcodeAreaCell.font = { name: 'Courier New', size: 8 };
  barcodeAreaCell.alignment = { vertical: 'middle', horizontal: 'center' };
  barcodeAreaCell.border = borderStyle;

  // 第二行：品名和件数
  const row2 = worksheet.getRow(startRow + 1);
  row2.height = 30;

  // 品名 Item Name
  const itemNameLabelCell = worksheet.getCell(`A${startRow + 1}`);
  itemNameLabelCell.value = '品名 Item Name';
  itemNameLabelCell.font = { name: 'Arial', size: 10, bold: true };
  itemNameLabelCell.alignment = { vertical: 'middle', horizontal: 'center' };
  itemNameLabelCell.border = borderStyle;

  // 品名值占位符（使用单号）
  worksheet.mergeCells(`B${startRow + 1}:C${startRow + 1}`);
  const itemNameValueCell = worksheet.getCell(`B${startRow + 1}`);
  itemNameValueCell.value = '{{ORDER_NO}}';
  itemNameValueCell.font = { name: 'Arial', size: 14, bold: true };
  itemNameValueCell.alignment = { vertical: 'middle', horizontal: 'center' };
  itemNameValueCell.border = borderStyle;

  // 品名二维码
  const itemQrCell = worksheet.getCell(`D${startRow + 1}`);
  itemQrCell.value = '品名二维码';
  itemQrCell.font = { name: 'Arial', size: 10 };
  itemQrCell.alignment = { vertical: 'middle', horizontal: 'center' };
  itemQrCell.border = borderStyle;

  // 二维码区域占位符
  worksheet.mergeCells(`E${startRow + 1}:G${startRow + 1}`);
  const qrAreaCell = worksheet.getCell(`E${startRow + 1}`);
  qrAreaCell.value = '{{QR_CODE}}';
  qrAreaCell.font = { name: 'Arial', size: 8 };
  qrAreaCell.alignment = { vertical: 'middle', horizontal: 'center', wrapText: true };
  qrAreaCell.border = borderStyle;

  // 第三行：件数
  const row3 = worksheet.getRow(startRow + 2);
  row3.height = 30;

  // 件数 PACKAGES
  const packagesLabelCell = worksheet.getCell(`A${startRow + 2}`);
  packagesLabelCell.value = '件数 PACKAGES';
  packagesLabelCell.font = { name: 'Arial', size: 10, bold: true };
  packagesLabelCell.alignment = { vertical: 'middle', horizontal: 'center' };
  packagesLabelCell.border = borderStyle;

  // 件数值占位符
  worksheet.mergeCells(`B${startRow + 2}:C${startRow + 2}`);
  const packagesValueCell = worksheet.getCell(`B${startRow + 2}`);
  packagesValueCell.value = '{{QUANTITY}}';
  packagesValueCell.font = { name: 'Arial', size: 14, bold: true };
  packagesValueCell.alignment = { vertical: 'middle', horizontal: 'center' };
  packagesValueCell.border = borderStyle;

  // 空白区域
  worksheet.mergeCells(`D${startRow + 2}:G${startRow + 2}`);
  const emptyCell = worksheet.getCell(`D${startRow + 2}`);
  emptyCell.border = borderStyle;

  // 添加仓库地址信息
  const infoStartRow = startRow + 4;
  
  // 仓库地址
  worksheet.mergeCells(`A${infoStartRow}:G${infoStartRow}`);
  const warehouseCell = worksheet.getCell(`A${infoStartRow}`);
  warehouseCell.value = '1. Warehouse address 仓库地址：佛山顺德区乐从镇新隆村第一工业区内 101';
  warehouseCell.font = { name: 'Arial', size: 10 };
  warehouseCell.alignment = { vertical: 'middle', horizontal: 'left' };

  // Sean联系方式
  worksheet.mergeCells(`A${infoStartRow + 1}:G${infoStartRow + 1}`);
  const seanCell = worksheet.getCell(`A${infoStartRow + 1}`);
  seanCell.value = 'Sean: +8618188802770';
  seanCell.font = { name: 'Arial', size: 10 };
  seanCell.alignment = { vertical: 'middle', horizontal: 'center' };

  // 上班时间
  worksheet.mergeCells(`A${infoStartRow + 2}:G${infoStartRow + 2}`);
  const timeCell = worksheet.getCell(`A${infoStartRow + 2}`);
  timeCell.value = '2. 上班时间：周一到周六，上午 10 点到下午 6 点。';
  timeCell.font = { name: 'Arial', size: 10 };
  timeCell.alignment = { vertical: 'middle', horizontal: 'center' };

  // 注意事项1
  worksheet.mergeCells(`A${infoStartRow + 3}:G${infoStartRow + 3}`);
  const notice1Cell = worksheet.getCell(`A${infoStartRow + 3}`);
  notice1Cell.value = '3. 本仓库不收到付件，不收无唛头的件。';
  notice1Cell.font = { name: 'Arial', size: 10 };
  notice1Cell.alignment = { vertical: 'middle', horizontal: 'center' };

  // 注意事项2
  worksheet.mergeCells(`A${infoStartRow + 4}:G${infoStartRow + 4}`);
  const notice2Cell = worksheet.getCell(`A${infoStartRow + 4}`);
  notice2Cell.value = '4. 仓库没有免费卸货服务，如需卸货，请联系仓库咨询费用。';
  notice2Cell.font = { name: 'Arial', size: 10 };
  notice2Cell.alignment = { vertical: 'middle', horizontal: 'center' };

  // 添加大号唛头显示占位符
  const bigMarkRow = startRow + 10;
  worksheet.mergeCells(`A${bigMarkRow}:G${bigMarkRow + 2}`);
  const bigMarkCell = worksheet.getCell(`A${bigMarkRow}`);
  bigMarkCell.value = '{{BIG_MARK_NUMBER}}';
  bigMarkCell.font = { name: 'Arial', size: 72, bold: true };
  bigMarkCell.alignment = { vertical: 'middle', horizontal: 'center' };
  
  // 设置大号唛头行高
  worksheet.getRow(bigMarkRow).height = 60;
  worksheet.getRow(bigMarkRow + 1).height = 60;
  worksheet.getRow(bigMarkRow + 2).height = 60;

  // 保存模板文件
  const templatePath = path.join(__dirname, '../public/templates/delivery-note-template.xlsx');
  
  // 确保目录存在
  const templateDir = path.dirname(templatePath);
  if (!fs.existsSync(templateDir)) {
    fs.mkdirSync(templateDir, { recursive: true });
  }

  await workbook.xlsx.writeFile(templatePath);
  console.log('Excel模板已生成:', templatePath);
}

// 运行生成脚本
generateDeliveryNoteTemplate().catch(console.error);
