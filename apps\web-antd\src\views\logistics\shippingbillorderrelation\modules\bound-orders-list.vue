<script lang="ts" setup>
import type { ShippingBillOrderRelationApi } from '#/api/logistics/shippingbillorderrelation';

import { ref, watch } from 'vue';

import { RefreshCw } from '@vben/icons';

import { message } from 'ant-design-vue';

import {
  getBillOrderRelations,
  unbindOrderFromBill,
} from '#/api/logistics/shippingbillorderrelation';

interface Props {
  billNo: string;
}

interface Emits {
  (e: 'refresh'): void;
  (
    e: 'update',
    orders: ShippingBillOrderRelationApi.ShippingBillOrderRelationVO[],
  ): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 绑定关系相关
const boundOrders = ref<
  ShippingBillOrderRelationApi.ShippingBillOrderRelationVO[]
>([]);
const loading = ref(false);

// 获取已绑定订单
async function fetchBoundOrders() {
  if (!props.billNo) {
    boundOrders.value = [];
    emit('update', []);
    return;
  }

  loading.value = true;
  try {
    const res = await getBillOrderRelations(props.billNo);
    boundOrders.value = res || [];
    emit('update', boundOrders.value);
  } catch (error) {
    console.error('获取绑定关系失败:', error);
    message.error('获取绑定关系失败');
    boundOrders.value = [];
    emit('update', []);
  } finally {
    loading.value = false;
  }
}

// 解绑订单
async function handleUnbindOrder(orderNo: string) {
  try {
    await unbindOrderFromBill(props.billNo, orderNo);
    message.success('解绑成功');

    // 刷新绑定关系
    await fetchBoundOrders();
    emit('refresh');
  } catch (error) {
    console.error('解绑失败:', error);
    message.error('解绑失败');
  }
}

// 刷新绑定关系
function handleRefresh() {
  fetchBoundOrders();
}

// 监听 billNo 变化
watch(
  () => props.billNo,
  (newBillNo) => {
    if (newBillNo) {
      fetchBoundOrders();
    } else {
      boundOrders.value = [];
    }
  },
  { immediate: true },
);

defineExpose({
  fetchBoundOrders,
});
</script>

<template>
  <a-card size="small">
    <template #title>
      <span>已绑定订单 ({{ boundOrders.length }})</span>
    </template>

    <template #extra>
      <a-button
        type="text"
        size="small"
        @click="handleRefresh"
        :loading="loading"
      >
        <template #icon>
          <RefreshCw />
        </template>
        刷新
      </a-button>
    </template>

    <div
      v-if="boundOrders.length === 0"
      style="text-align: center; padding: 16px 0"
    >
      <a-empty description="暂无绑定订单" :image="false" />
    </div>

    <div v-else style="display: flex; flex-direction: column; gap: 8px">
      <a-card
        v-for="order in boundOrders"
        :key="order.orderNo"
        size="small"
        style="margin-bottom: 8px"
      >
        <div
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
          "
        >
          <div>
            <div style="font-weight: 500">{{ order.orderNo }}</div>
            <div
              style="font-size: 12px; color: #666"
              v-if="order.outstandingCharges"
            >
              未结清费用: ¥{{ order.outstandingCharges }}
            </div>
          </div>
          <a-popconfirm
            title="确认解绑此订单？"
            @confirm="() => handleUnbindOrder(order.orderNo)"
          >
            <a-button type="link" danger size="small"> 解绑 </a-button>
          </a-popconfirm>
        </div>
      </a-card>
    </div>
  </a-card>
</template>
