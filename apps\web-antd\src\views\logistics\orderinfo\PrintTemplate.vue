<template>
  <div class="print-template p-0 m-0 w-full" style="width: 210mm; min-height: 297mm; font-size: 15px;">
    <!-- 标题和logo -->
    <div class="flex items-center justify-between mb-4">
      <img v-if="logo" :src="logo" alt="公司Logo" class="h-12" />
      <div class="text-2xl font-bold text-center flex-1">物流入仓单</div>
      <div class="w-12"></div>
    </div>
    <!-- 主表信息双列 -->
    <div class="grid grid-cols-2 gap-x-8 mb-4 text-base">
      <div>
        <div>单号：{{ orderInfo.orderNo }}</div>
        <div>发货方：{{ orderInfo.supplierName }}</div>
        <div>发货电话：{{ orderInfo.supplierTel }}</div>
        <div>收货方：{{ orderInfo.customerName }}</div>
        <div>收货电话：{{ orderInfo.customerTel }}</div>
      </div>
      <div>
        <div>日期：{{ orderInfo.createTime || '-' }}</div>
        <div>运输方式：{{ getShippingMethod(orderInfo.shippingMethod) }}</div>
        <div>入库仓库：{{ orderInfo.warehouse }}</div>
        <div>唛头：{{ orderInfo.markNumber }}</div>
        <div>物流条码：{{ orderInfo.trackingNumber }}</div>
      </div>
    </div>
    <!-- 明细表格 -->
    <table class="w-full border border-collapse mb-6 text-base">
      <thead>
        <tr class="bg-gray-100">
          <th class="border p-1 w-12">序号</th>
          <th class="border p-1">商品名称</th>
          <th class="border p-1">数量</th>
          <th class="border p-1">备注</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(item, idx) in orderItems" :key="idx">
          <td class="border p-1 text-center">{{ idx + 1 }}</td>
          <td class="border p-1">{{ item.itemName }}</td>
          <td class="border p-1 text-center">{{ item.quantity }}</td>
          <td class="border p-1"></td>
        </tr>
      </tbody>
    </table>
    <!-- 签字栏 -->
    <div class="flex justify-between mt-8">
      <div>经办人签字：</div>
      <div>收货人签字：</div>
      <div>日期：</div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  orderInfo: Record<string, any>,
  orderItems: any[],
  logo?: string
}>();

function getShippingMethod(val: any) {
  if (val === 1) return '空运';
  if (val === 2) return '海运';
  if (val === 3) return '陆运';
  return val || '-';
}
</script>

<style scoped>
.print-template {
  font-family: 'Microsoft YaHei', Arial, sans-serif;
}
@media print {
  .print-template {
    box-shadow: none !important;
    border: none !important;
    background: white !important;
    margin: 0 !important;
    padding: 0 !important;
    width: 210mm !important;
    min-width: 210mm !important;
    max-width: 210mm !important;
    min-height: 297mm !important;
    max-height: 297mm !important;
    font-size: 15px !important;
  }
}
</style> 
