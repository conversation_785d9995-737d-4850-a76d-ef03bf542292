import type { PageParam, PageResult } from '@vben/request';

import { requestClient } from '#/api/request';

export namespace MarkInfoApi {
  // 物流联系信息 VO
  export interface MarkInfo {
    id: number // 主键
    number: string // 唛头
    phone: string // 联系电话
  }
}


/** 查询物流联系信息分页 */
export function getMarkInfoPage(params: PageParam) {
  return requestClient.get<PageResult<MarkInfoApi.MarkInfo>>(
    '/logistics/mark-info/page',
    { params },
  );
}


/** 查询物流联系信息详情 */
export function getMarkInfo(id: number){
  return requestClient.get<MarkInfoApi.MarkInfo>(
    `/logistics/mark-info/get?id=${id}`
  );
}

/** 新增物流联系信息 */
export function createMarkInfo(data: MarkInfoApi.MarkInfo) {
  return requestClient.post('/logistics/mark-info/create', data);
}

/** 修改物流联系信息 */
export function updateMarkInfo(data: MarkInfoApi.MarkInfo) {
  return requestClient.put('/logistics/mark-info/update', data);
}

/** 删除物流联系信息 */
export function deleteMarkInfo(id: number) {
  return requestClient.delete(`/logistics/mark-info/delete?id=${id}`);
}

/** 导出物流联系信息 */
export function exportMarkInfo(params: any) {
  return requestClient.download('/logistics/mark-info/export', params);
}


