<script lang="ts" setup>
import type { ShippingBillInfoApi } from '#/api/logistics/shippingbillinfo';

import { computed, onMounted, ref } from 'vue';

import { message } from 'ant-design-vue';

import { getShippingBillInfoPage } from '#/api/logistics/shippingbillinfo';

interface Props {
  selectedBillNo?: string;
}

interface Emits {
  (
    e: 'select',
    billNo: string,
    billInfo?: ShippingBillInfoApi.ShippingBillInfoVO,
  ): void;
}

const props = withDefaults(defineProps<Props>(), {
  selectedBillNo: '',
});

const emit = defineEmits<Emits>();

// 搜索文本
const searchText = ref('');

// 提单相关
const billList = ref<ShippingBillInfoApi.ShippingBillInfoVO[]>([]);
const loading = ref(false);
const pagination = ref({ current: 1, pageSize: 10, total: 0 });

// 表格列定义
const columns = [
  {
    title: '提单编号',
    dataIndex: 'billOfLadingNumber',
    key: 'billOfLadingNumber',
    width: 160,
  },
  {
    title: '物流编号',
    dataIndex: 'trackingNumber',
    key: 'trackingNumber',
    width: 160,
  },
  { title: '创建时间', dataIndex: 'createTime', key: 'createTime', width: 120 },
];

// 过滤后的列表
const filteredBillList = computed(() => {
  if (!searchText.value) return billList.value;
  return billList.value.filter((bill) =>
    bill.billOfLadingNumber
      .toLowerCase()
      .includes(searchText.value.toLowerCase()),
  );
});

// 获取提单列表
async function fetchBillList(paginationParams = pagination.value) {
  loading.value = true;
  try {
    const res = await getShippingBillInfoPage({
      pageNo: paginationParams.current,
      pageSize: paginationParams.pageSize,
    });
    billList.value = res.list || [];
    pagination.value.total = res.total;
    pagination.value.current = paginationParams.current;
    pagination.value.pageSize = paginationParams.pageSize;
  } catch (error) {
    console.error('获取提单列表失败:', error);
    message.error('获取提单列表失败');
    billList.value = [];
  } finally {
    loading.value = false;
  }
}

// 提单选择
function onBillSelect(selectedRowKeys: string[]) {
  const billNo = selectedRowKeys[0];
  const billInfo = billList.value.find(
    (bill) => bill.billOfLadingNumber === billNo,
  );
  emit('select', billNo || '', billInfo);
}

// 表格变化处理
function onTableChange(paginationParams: any) {
  fetchBillList(paginationParams);
}

// 搜索处理
function handleSearch() {
  // 搜索是通过计算属性实现的，这里可以添加额外逻辑
}

// 初始化
onMounted(() => {
  fetchBillList();
});
</script>

<template>
  <a-card size="small" title="提单列表">
    <template #extra>
      <a-input-search
        v-model:value="searchText"
        placeholder="搜索提单编号"
        style="width: 200px"
        @search="handleSearch"
        allow-clear
      />
    </template>

    <a-table
      row-key="billOfLadingNumber"
      :columns="columns"
      :data-source="filteredBillList"
      :pagination="pagination"
      :loading="loading"
      :row-selection="{
        type: 'radio',
        selectedRowKeys: [props.selectedBillNo],
        onChange: onBillSelect,
      }"
      @change="onTableChange"
      bordered
      size="small"
      :scroll="{ y: 400 }"
    />
  </a-card>
</template>
