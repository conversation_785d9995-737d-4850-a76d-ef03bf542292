<script lang="ts" setup>
import type { ShippingBillInfoApi } from '#/api/logistics/shippingbillinfo';

interface Props {
  billInfo?: ShippingBillInfoApi.ShippingBillInfoVO;
}

const props = defineProps<Props>();
</script>

<template>
  <a-card size="small" title="当前提单信息" v-if="props.billInfo">
    <a-descriptions :column="2" size="small" bordered>
      <a-descriptions-item label="提单编号">
        {{ props.billInfo.billOfLadingNumber }}
      </a-descriptions-item>
      <a-descriptions-item label="物流编号">
        {{ props.billInfo.trackingNumber }}
      </a-descriptions-item>
      <a-descriptions-item label="创建时间" :span="2">
        {{ props.billInfo.createTime }}
      </a-descriptions-item>
    </a-descriptions>
  </a-card>
</template>
