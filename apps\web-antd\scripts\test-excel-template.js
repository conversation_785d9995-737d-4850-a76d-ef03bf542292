import ExcelJS from 'exceljs';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * 测试Excel模板填充功能
 */
async function testExcelTemplate() {
  try {
    // 读取模板文件
    const templatePath = path.join(__dirname, '../public/templates/delivery-note-template.xlsx');
    
    if (!fs.existsSync(templatePath)) {
      console.error('模板文件不存在:', templatePath);
      return;
    }

    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(templatePath);
    
    const worksheet = workbook.getWorksheet('Delivery Note');
    if (!worksheet) {
      console.error('未找到 "Delivery Note" 工作表');
      return;
    }

    console.log('✅ 模板文件读取成功');

    // 测试数据
    const testData = {
      '{{SERIAL_NUMBER}}': '679',
      '{{MARK_NUMBER}}': '12345-SEA',
      '{{SHIPPING_METHOD}}': '●SEA海运\n○AIR空运',
      '{{BARCODE}}': '|||12345-SEA|||',
      '{{ORDER_NO}}': 'ORD-2025-001',
      '{{QR_CODE}}': '■■■■■■■■■\n■■■■■■■■■\n■■■■■■■■■',
      '{{QUANTITY}}': '5',
      '{{BIG_MARK_NUMBER}}': '12345-SEA',
    };

    let replacementCount = 0;

    // 遍历所有单元格，替换占位符
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        if (cell.value && typeof cell.value === 'string') {
          let cellValue = cell.value;
          for (const [placeholder, replacement] of Object.entries(testData)) {
            if (cellValue.includes(placeholder)) {
              cellValue = cellValue.replace(placeholder, replacement);
              replacementCount++;
            }
          }
          if (cellValue !== cell.value) {
            cell.value = cellValue;
          }
        }
      });
    });

    console.log(`✅ 完成 ${replacementCount} 个占位符替换`);

    // 保存测试文件
    const outputPath = path.join(__dirname, '../public/test-output.xlsx');
    await workbook.xlsx.writeFile(outputPath);
    
    console.log('✅ 测试文件已生成:', outputPath);
    console.log('🎉 Excel模板填充功能测试成功！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
testExcelTemplate();
