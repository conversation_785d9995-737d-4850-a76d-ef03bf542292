import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { OrderInfoApi } from '#/api/logistics/orderinfo';
import { z } from '#/adapter/form';

declare global {
  interface Window {
    __vbenFormApi: any;
  }
}

/** 新增/修改的表单 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    { fieldName: 'id', component: 'Input', dependencies: { triggerFields: [''], show: () => false } },
    { fieldName: 'orderNo', label: '单号', component: 'Input', componentProps: { readonly: true, placeholder: '系统自动生成' } },
    { fieldName: 'orderNoQr', label: '单号二维码', component: 'Render' },
    { fieldName: 'supplierName', label: '供应商名称', rules: 'required', component: 'Input', componentProps: { placeholder: '请输入供应商名称' } },
    { fieldName: 'supplierTel', label: '供应商电话', rules: z.string().regex(/^[0-9+\s()]*$/, '仅允许输入数字、+、空格和括号').refine(val => (val.match(/\d/g)?.length ?? 0) > 5 && val.length > 0, '请输入电话且需包含至少6位数字'), component: 'Input', componentProps: { placeholder: '请输入供应商电话' } },
    { fieldName: 'customerName', label: '客户名称', rules: 'required', component: 'Input', componentProps: { placeholder: '请输入客户名称' } },
    { fieldName: 'customerTel', label: '客户电话', rules: z.string().regex(/^[0-9+\s()]*$/, '仅允许输入数字、+、空格和括号').refine(val => (val.match(/\d/g)?.length ?? 0) > 5 && val.length > 0, '请输入电话且需包含至少6位数字'), component: 'Input', componentProps: {
      placeholder: '请输入客户电话',
      onChange: (e: any) => {
        const val = e?.target?.value ?? e;
        const digits = (val || '').replace(/\D/g, '');
        if (typeof window !== 'undefined' && window.__vbenFormApi) {
          window.__vbenFormApi.setFieldValue('markNumber', digits.slice(-5));
          if (window.__vbenMarkNumberQrRef) {
            window.__vbenMarkNumberQrRef.value = digits.slice(-5);
          }
        }
      }
    } },
    { fieldName: 'markNumber', label: '唛头', rules: 'required', component: 'Input', componentProps: { readonly: true, placeholder: '自动截取客户电话后5位' } },
    { fieldName: 'markNumberQr', label: '唛头二维码', component: 'Render' },
    { fieldName: 'trackingNumber', label: '物流条码', component: 'Input', componentProps: { placeholder: '请输入物流条码' } },
    { fieldName: 'warehouse', label: '入库仓库', rules: 'required', component: 'Input', componentProps: { placeholder: '请输入入库仓库' } },
    { fieldName: 'shippingMethod', label: '运输方式', rules: 'required', component: 'Select', componentProps: { placeholder: '请选择运输方式', options: [ { label: '空运', value: 1 }, { label: '海运', value: 2 }, { label: '陆运', value: 3 } ] } },
    { fieldName: 'quantity', label: '包装件数', rules: z.number().gt(0, '包装件数必须大于0'), component: 'InputNumber', componentProps: { placeholder: '请输入包装件数' } },
    { fieldName: 'volume', label: '体积(CBM) m³', rules: z.number().gt(0, '体积必须大于0').refine(val => Number(val.toFixed(3)) === val, '最多3位小数'), component: 'InputNumber', componentProps: { placeholder: '请输入体积(CBM)', step: 0.001, precision: 3 } },
    { fieldName: 'weight', label: '重量(kg)', rules: z.number().gt(0, '重量必须大于0').refine(val => Number(val.toFixed(3)) === val, '最多3位小数'), component: 'InputNumber', componentProps: { placeholder: '请输入重量', step: 0.001, precision: 3 } },
    { fieldName: 'imgUrl', label: '图片', component: 'ImageUpload', componentProps: { maxNumber: 1, fileSize: 5, fileType: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'], width: '120px', height: '80px' } },
  ];
}

/** 列表的搜索表单 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    { fieldName: 'orderNo', label: '单号', component: 'Input', componentProps: { allowClear: true, placeholder: '请输入单号' } },
    { fieldName: 'supplierName', label: '供应商名称', component: 'Input', componentProps: { allowClear: true, placeholder: '请输入供应商名称' } },
    { fieldName: 'supplierTel', label: '供应商电话', component: 'Input', rules: z.string().regex(/^[0-9+\s()]*$/, '仅允许输入数字、+、空格和括号').optional(), componentProps: { allowClear: true, placeholder: '请输入供应商电话' } },
    { fieldName: 'customerName', label: '客户名称', component: 'Input', componentProps: { allowClear: true, placeholder: '请输入客户名称' } },
    { fieldName: 'customerTel', label: '客户电话', component: 'Input', rules: z.string().regex(/^[0-9+\s()]*$/, '仅允许输入数字、+、空格和括号').optional(), componentProps: { allowClear: true, placeholder: '请输入客户电话' } },
    { fieldName: 'markNumber', label: '唛头', component: 'Input', componentProps: { allowClear: true, placeholder: '请输入唛头' } },
    { fieldName: 'trackingNumber', label: '物流条码', component: 'Input', componentProps: { allowClear: true, placeholder: '请输入物流条码' } },
    { fieldName: 'warehouse', label: '入库仓库', component: 'Input', componentProps: { allowClear: true, placeholder: '请输入入库仓库' } },
    { fieldName: 'shippingMethod', label: '运输方式', component: 'Select', componentProps: { allowClear: true, placeholder: '请选择运输方式', options: [ { label: '空运', value: 1 }, { label: '海运', value: 2 }, { label: '陆运', value: 3 } ] } },
    { fieldName: 'imgUrl', label: '图片', component: 'Input', componentProps: { allowClear: true, placeholder: '请输入图片URL' } },
    { fieldName: 'createTime', label: '创建时间', component: 'RangePicker', componentProps: { allowClear: true } },
  ];
}

/** 列表的字段 */
export function useGridColumns(): VxeTableGridOptions<OrderInfoApi.OrderInfoVO>['columns'] {
  return [
    { type: 'checkbox', width: 40 },
    { field: 'id', title: '主键', minWidth: 80 },
    { field: 'orderNo', title: '单号', minWidth: 120 },
    { field: 'supplierName', title: '供应商名称', minWidth: 120 },
    { field: 'supplierTel', title: '供应商电话', minWidth: 120 },
    { field: 'customerName', title: '客户名称', minWidth: 120 },
    { field: 'customerTel', title: '客户电话', minWidth: 120 },
    { field: 'markNumber', title: '唛头', minWidth: 120 },
    { field: 'trackingNumber', title: '物流条码', minWidth: 120 },
    { field: 'warehouse', title: '入库仓库', minWidth: 120 },
    { field: 'shippingMethod', title: '运输方式', minWidth: 100 },
    { field: 'quantity', title: '包装件数', minWidth: 100 },
    { field: 'volume', title: '体积(CBM)', minWidth: 100 },
    { field: 'weight', title: '重量', minWidth: 100 },
    { field: 'imgUrl', title: '图片', minWidth: 100, cellRender: { name: 'CellImage' } },
    { field: 'createTime', title: '创建时间', minWidth: 120, formatter: 'formatDateTime' },
    { field: 'updateTime', title: '更新时间', minWidth: 120, formatter: 'formatDateTime' },
    { field: 'creator', title: '创建者', minWidth: 100 },
    { field: 'updater', title: '更新者', minWidth: 100 },
    { title: '操作', width: 160, fixed: 'right', slots: { default: 'actions' } },
  ];
}
