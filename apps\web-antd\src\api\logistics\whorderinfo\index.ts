import type { PageParam, PageResult } from '@vben/request';

import { requestClient } from '#/api/request';

export namespace WhOrderInfoApi {
  // 物流订单入仓 VO
  export interface WhOrderInfoVO {
    id: number; // 主键
    orderNo: string; // 单号
    supplierName: string; // 供应商名称
    supplierTel: string; // 供应商电话
    customerName: string; // 客户名称
    customerTel: string; // 客户电话
    markNumber: string; // 唛头 电话后5位
    trackingNumber: string; // 物流条码
    warehouse: string; // 入库仓库
    shippingMethod: number; // 运输方式
    quantity: number; // 包装件数
    volume: number; // 体积
    weight: number; // 重量
    imgUrl: string; // 图片
    createTime: string; // 创建时间
    creator: string; // 创建者
    updater: string; // 更新者
    updateTime: string; // 更新时间
    deleted: boolean; // 是否删除
  }
}

/** 查询物流订单入仓分页 */
export function getWhOrderInfoPage(params: PageParam) {
  return requestClient.get<PageResult<WhOrderInfoApi.WhOrderInfoVO>>(
    '/logistics/order/warehouse-in/list',
    { params },
  );
}

/** 查询物流订单入仓详情 */
export function getWhOrderInfo(id: number) {
  return requestClient.get<WhOrderInfoApi.WhOrderInfoVO>(
    `/logistics/order/get?id=${id}`,
  );
}

/** 修改物流订单入仓 */
export function updateWhOrderInfo(data: WhOrderInfoApi.WhOrderInfoVO) {
  return requestClient.put('/logistics/order/update', data);
}

/** 删除物流订单入仓 */
export function deleteWhOrderInfo(id: number) {
  return requestClient.delete(`/logistics/order/delete?id=${id}`);
}

/** 导出物流订单入仓 */
export function exportWhOrderInfo(params: any) {
  return requestClient.download('/logistics/order/export-excel', params);
}

/** 获取仓库列表 */
export function getWarehouseList() {
  return requestClient.get<string[]>('/logistics/order/warehouses');
}
